﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace WatchTower.Common.Extensions.HealthCheck
{
    public class DatabaseHealthCheck<TContext> : IHealthCheck where TContext : DbContext
    {
        private readonly TContext _context;

        public DatabaseHealthCheck(TContext context)
        {
            _context = context;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                await _context.Database.CanConnectAsync(cancellationToken);
                var dbName = _context.Database.GetDbConnection().Database;
                return HealthCheckResult.Healthy($"Connected to database: {dbName}");
            }
            catch
            {
                return HealthCheckResult.Unhealthy("Failed to connect to database");
            }
        }
    }
}
