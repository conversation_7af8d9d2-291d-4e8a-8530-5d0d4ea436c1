﻿using WatchTower.Common.Requests;
using WatchTower.Models;

namespace WatchTower.Services.Interfaces
{
    public interface IWatchHealthCheckService
    {
        Task<AddWatchHealthCheckRequest> AddWatchHealthCheckServiceAsync(AddWatchHealthCheckRequest watchHealthCheckRequest, CancellationToken cancellationToken);

        Task<WatchHealthCheckDTO> UpdateAsync(UpdateWatchHealthCheckRequest watchHealthCheckRequest, CancellationToken cancellationToken);
        
        Task<List<WatchHealthCheckDTO>> GetAllAsync(CancellationToken cancellationToken);

        Task<List<WatchHealthCheckDTO>> GetUpdatedAsync(CancellationToken cancellationToken);

        Task CheckServiceHealthAsync(WatchHealthCheckDTO healthCheckRecord, CancellationToken cancellationToken);
    }
}
