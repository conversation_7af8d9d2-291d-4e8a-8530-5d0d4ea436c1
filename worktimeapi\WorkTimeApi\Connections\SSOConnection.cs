using WorkTimeApi.Connections.Interfaces;

namespace WorkTimeApi.Connections
{
    public class SSOConnection : ISSOConnection
    {
        private readonly HttpClient _httpClient;

        public SSOConnection(IConfiguration configuration, HttpClient httpClient)
        {
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(configuration["SSOBaseUrl"]!);
        }

        public Task<HttpResponseMessage> AuthenticateAsync(string authorizationHeader, string refreshTokenHeader)
        {
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            _httpClient.DefaultRequestHeaders.Remove("refresh-token");
            _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            _httpClient.DefaultRequestHeaders.Add("refresh-token", refreshTokenHeader);

            return _httpClient.GetAsync("sso/auth");
        }

        public Task<HttpResponseMessage> GetUserPermissionsByUserRegistrationsCompanyIdAsync(int companyId, string token)
        {
            return _httpClient.GetAsync($"sso/companies/{companyId}/permissions/{token}");
        }

    }
}
