﻿using WatchTower.Api.Services.Interfaces;

namespace WatchTower.Api.Workers.Cleaners
{
    public class HealthCheckCleaner(IHealthCheckService healthCheckService) : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await healthCheckService.CleanAsync(stoppingToken);

                await Task.Delay(TimeSpan.FromHours(12), stoppingToken);
            }
        }
    }
}
