﻿namespace Gateway.Extenstions.HttpClientExtensions
{
    public static class HttpClientExtension
    {
        public static async Task<HttpResponseMessage> GetAuthAsync(this HttpClient client, string requestUri, IHttpContextAccessor httpContextAccessor)
        {
            var authorizationHeader = httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault();
            var refreshTokenHeader = httpContextAccessor.HttpContext.Request.Headers["Refresh-Token"].FirstOrDefault();
            var companyIdHeader = httpContextAccessor.HttpContext.Request.Headers["company-id"].FirstOrDefault();

            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                client.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            }

            if (!string.IsNullOrEmpty(refreshTokenHeader))
            {
                client.DefaultRequestHeaders.Add("refresh-token", refreshTokenHeader);
            }

            if (!string.IsNullOrEmpty(companyIdHeader))
            {
                client.DefaultRequestHeaders.Add("company-id", companyIdHeader);
            }

            return await client.GetAsync(requestUri);
        }

        public static Task<HttpResponseMessage> PostAsJsonAuthAsync(this HttpClient client, string requestUri, object content, IHttpContextAccessor httpContextAccessor)
        {
            var authorizationHeader = httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault();
            var refreshTokenHeader = httpContextAccessor.HttpContext.Request.Headers["Refresh-Token"].FirstOrDefault();
            var companyIdHeader = httpContextAccessor.HttpContext.Request.Headers["company-id"].FirstOrDefault();

            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                client.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            }

            if (!string.IsNullOrEmpty(refreshTokenHeader))
            {
                client.DefaultRequestHeaders.Add("refresh-token", refreshTokenHeader);
            }

            if (!string.IsNullOrEmpty(companyIdHeader))
            {
                client.DefaultRequestHeaders.Add("company-id", companyIdHeader);
            }

            return client.PostAsJsonAsync(requestUri, content);
        }
    }
}
