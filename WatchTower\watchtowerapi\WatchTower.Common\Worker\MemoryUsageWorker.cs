﻿using Microsoft.Extensions.Hosting;

namespace WatchTower.Common.Worker
{
    public class MemoryUsageWorker : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            //var meter = new Meter("MemoryUsage");
            //var counter = meter.CreateUpDownCounter<double>("memory_usage_megabytes");

            //var memoryUsageBytes = 0.0;

            //while (!stoppingToken.IsCancellationRequested)
            //{
            //    counter.Add(-memoryUsageBytes);

            //    memoryUsageBytes = GC.GetTotalMemory(false) / 1024.0 / 1024.0;
            //    counter.Add(memoryUsageBytes);

            //    await Task.Delay(TimeSpan.FromSeconds(1), stoppingToken);
            //}
        }
    }
}
