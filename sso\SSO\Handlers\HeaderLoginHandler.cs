﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Handlers
{
    public class HeaderLoginHandler : IRequestHandler<HeaderLoginRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;

        public HeaderLoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
        }

        public async Task<IResult> Handle(HeaderLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var authorizationHeader = request.HttpRequest.Headers.Authorization;

            var response = await _userRegistrationsConnection.TryLoginWithResponseAsync(authorizationHeader.ToString());
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return Results.NotFound("Invalid Credentials!");

            if (!response.IsSuccessStatusCode)
                return Results.BadRequest("Unknown error!");

            var userDTO = JsonSerializer.Deserialize<UserDTO>(await response.Content.ReadAsStringAsync(cancellationToken));
            if (userDTO is null)
                return Results.BadRequest();

            request.HttpRequest.HttpContext.Response.Headers.Append("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await _jwtGeneratorService.GenerateRefreshTokenAsync(userDTO);

            if (string.IsNullOrEmpty(userDTO.Email))
                return Results.BadRequest("Invalid email address");

            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email);
            request.HttpRequest.HttpContext.Response.Headers.Append("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
