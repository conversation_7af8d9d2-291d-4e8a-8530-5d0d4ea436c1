﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WatchTower.Database;

#nullable disable

namespace WatchTower.Database.Migrations
{
    [DbContext(typeof(WatchTowerDbContext))]
    partial class WatchTowerDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("WatchTower.Database.Models.HealthCheck", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("WatchHealthCheckId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WatchHealthCheckId");

                    b.ToTable("HealthChecks");
                });

            modelBuilder.Entity("WatchTower.Database.Models.HealthCheckEntry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan>("Duration")
                        .HasColumnType("time");

                    b.Property<Guid>("HealthCheckId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("HealthCheckId");

                    b.ToTable("HealthCheckEntry");
                });

            modelBuilder.Entity("WatchTower.Database.Models.WatchHealthCheck", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Environment")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FailedChecksCountToNotify")
                        .HasColumnType("int");

                    b.Property<bool>("IsUpdated")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan>("PollingInterval")
                        .HasColumnType("time");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("WatchHealthChecks");
                });

            modelBuilder.Entity("WatchTower.Database.Models.HealthCheck", b =>
                {
                    b.HasOne("WatchTower.Database.Models.WatchHealthCheck", "WatchHealthCheck")
                        .WithMany("HealthChecks")
                        .HasForeignKey("WatchHealthCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WatchHealthCheck");
                });

            modelBuilder.Entity("WatchTower.Database.Models.HealthCheckEntry", b =>
                {
                    b.HasOne("WatchTower.Database.Models.HealthCheck", "HealthCheck")
                        .WithMany("Entries")
                        .HasForeignKey("HealthCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthCheck");
                });

            modelBuilder.Entity("WatchTower.Database.Models.HealthCheck", b =>
                {
                    b.Navigation("Entries");
                });

            modelBuilder.Entity("WatchTower.Database.Models.WatchHealthCheck", b =>
                {
                    b.Navigation("HealthChecks");
                });
#pragma warning restore 612, 618
        }
    }
}
