﻿using Microsoft.Extensions.DependencyInjection;
using OpenTelemetry.Metrics;
using WatchTower.Common.Worker;

namespace WatchTower.Extenstions.MetricsExtensions
{
    public static class MetricsExtensions
    {
        public static IServiceCollection AddOpenTelemetryMetrics(this IServiceCollection services, params string[] additionalMeters)
        {
            services.AddHostedService<MemoryUsageWorker>();

            services
                .AddOpenTelemetry()
                .WithMetrics(x =>
                {
                    x.AddPrometheusExporter();

                    x.AddMeter("Microsoft.AspNetCore.Hosting", "Microsoft.AspNetCore.Server.Kestrel", "MemoryUsage");

                    x.AddMeter(additionalMeters);
                });

            return services;
        }
    }
}
