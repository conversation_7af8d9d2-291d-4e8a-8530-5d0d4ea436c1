﻿using AutoMapper;
using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Services.Interfaces.Notifiers;
using WatchTower.Common.Requests;
using WatchTower.Database.Models;
using WatchTower.Database.Repositorise.Interfaces;
using WatchTower.Models;
using WatchTower.Services.Interfaces;

namespace WatchTower.Services
{
    public class WatchHealthCheckService : IWatchHealthCheckService
    {
        private readonly IWatchHealthCheckRepository _watchHealthCheckRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<WatchHealthCheckService> _logger;
        private readonly IHealthCheckRepository _healthCheckRepository;
        private readonly INotify _notify;

        public WatchHealthCheckService(IWatchHealthCheckRepository watchHealthCheckRepository,
            IHealthCheckRepository healthCheckRepository,
            I<PERSON>apper mapper,
            ILogger<WatchHealthCheckService> logger,
            INotify notify)
        {
            _watchHealthCheckRepository = watchHealthCheckRepository;
            _healthCheckRepository = healthCheckRepository;
            _mapper = mapper;
            _logger = logger;
            _notify = notify;
        }

        public async Task CheckServiceHealthAsync(WatchHealthCheckDTO healthCheckRecordDTO, CancellationToken cancellationToken)
        {
            var failedChecks = 0;
            var healthCheckRecord = await _healthCheckRepository.Update(_mapper.Map<WatchHealthCheck>(healthCheckRecordDTO), cancellationToken);
            var lastStatus = healthCheckRecord.HealthChecks.OrderByDescending(hc => hc.CreatedDate).FirstOrDefault()?.Status ?? "Unknown";
            var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback= (message, cert, chain, errors) => true
            };

            var client = new HttpClient(handler)
            {
                BaseAddress = new Uri(healthCheckRecord.Url)
            };

            while (!cancellationToken.IsCancellationRequested)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    var response = await client.GetAsync(healthCheckRecord.Url, cancellationToken);
                    if (response is null)
                    {
                        _logger.LogError("Trying to check health for service: {FailedHealthCheckUrl} failed!", healthCheckRecord.Url);

                        continue;
                    }

                    if (!response.IsSuccessStatusCode)
                    {
                        _logger.LogError("{UnhealthyCheckUrl} is not healthy!", healthCheckRecord.Url);
                    }

                    var healthCheckDTO = await response.Content.ReadFromJsonAsync<HealthCheckDTO>(cancellationToken: cancellationToken);
                    if (healthCheckDTO is null)
                    {
                        _logger.LogError("{UnknownHealthCheck} is not recognizable!", healthCheckRecord.Url);
                        continue;
                    }

                    healthCheckDTO.WatchHealthCheckId = healthCheckRecord.Id;
                    healthCheckDTO.Environment = healthCheckRecord.Environment;

                    if ((lastStatus != healthCheckDTO.Status && lastStatus == "Unhealthy")
                        || (lastStatus == "Unknown" && lastStatus != healthCheckDTO.Status)
                        || (lastStatus == "Healthy" && lastStatus != healthCheckDTO.Status && failedChecks > healthCheckRecord.FailedChecksCountToNotify))
                    {
                        if (healthCheckDTO.Status == "Unhealthy" || lastStatus == "Unknown")
                            await _healthCheckRepository.AddAsync(_mapper.Map<HealthCheck>(healthCheckDTO));

                        await _notify.OnHealthCheckAdded(healthCheckRecord.Name, healthCheckDTO);
                        lastStatus = healthCheckDTO.Status;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(new EventId(), ex, "Trying to check health for service: {FailedHealthCheckUrl} failed!", healthCheckRecord.Url);
                    var healthCheck = new HealthCheck()
                    {
                        Status = "Unhealthy",
                        WatchHealthCheckId = healthCheckRecord.Id
                    };

                    var healthCheckDTO = _mapper.Map<HealthCheckDTO>(healthCheck);
                    healthCheckDTO.Environment = healthCheckRecord.Environment;

                    if (lastStatus != healthCheckDTO.Status && healthCheckRecord.FailedChecksCountToNotify > 5)
                    {
                        await _notify.OnHealthCheckAdded(healthCheckRecord.Name, healthCheckDTO);
                        lastStatus = healthCheckDTO.Status;

                        await _healthCheckRepository.AddAsync(healthCheck);
                    }
                }
                finally
                {
                    await Task.Delay(healthCheckRecord.PollingInterval, cancellationToken);
                }
            }
        }

        public async Task<AddWatchHealthCheckRequest> AddWatchHealthCheckServiceAsync(AddWatchHealthCheckRequest watchHealthCheckRequest, CancellationToken cancellationToken)
            => _mapper.Map<AddWatchHealthCheckRequest>(await _watchHealthCheckRepository.AddAsync(_mapper.Map<WatchHealthCheck>(watchHealthCheckRequest), cancellationToken));

        public async Task<List<WatchHealthCheckDTO>> GetAllAsync(CancellationToken cancellationToken)
            => _mapper.Map<List<WatchHealthCheckDTO>>(await _watchHealthCheckRepository.GetAllAsync(cancellationToken));

        public async Task<List<WatchHealthCheckDTO>> GetUpdatedAsync(CancellationToken cancellationToken)
            => _mapper.Map<List<WatchHealthCheckDTO>>(await _watchHealthCheckRepository.GetUpdatedAsync(cancellationToken));

        public async Task<WatchHealthCheckDTO> UpdateAsync(UpdateWatchHealthCheckRequest watchHealthCheckRequest, CancellationToken cancellationToken)
            => _mapper.Map<WatchHealthCheckDTO>(await _watchHealthCheckRepository.UpdateAsync(_mapper.Map<WatchHealthCheck>(watchHealthCheckRequest), cancellationToken));
    }
}
