﻿using Microsoft.AspNetCore.SignalR;
using WatchTower.Api.Hubs;
using WatchTower.Api.Hubs.Interfaces;
using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Notifiers.Interfaces.HealthChecks;

namespace WatchTower.Api.Notifiers.HealthChecks
{
    public class SignalRNotifier : ISignalRNotifier
    {
        private readonly IHubContext<HealthCheckHub, IHealthChecksClient> _actionsContext;

        public SignalRNotifier(IHubContext<HealthCheckHub, IHealthChecksClient> actionsContext)
        {
            _actionsContext = actionsContext;
        }

        public Task NotifyOnHealthCheckAddedAsync(string serviceName, HealthCheckDTO healthCheckDTO)
            => _actionsContext.Clients.All.OnHealthCheckAdded(healthCheckDTO);

        public Task NotifyOnServicesStopedAsync(string serviceName, ServerDTO serverDTO)
            => _actionsContext.Clients.All.OnServerServiceChecked(serviceName, serverDTO);
    }
}
