﻿using WatchTower.Models;
using WatchTower.Services.Interfaces;

namespace WatchTower.Workers
{
    public class _HealthChecksWorker : BackgroundService
    {
        private readonly IWatchHealthCheckService _watchHealthCheckService;
        private readonly Dictionary<Guid, Timer> _serviceTimers = new();

        public _HealthChecksWorker(IWatchHealthCheckService watchHealthCheckService)
        {
            _watchHealthCheckService = watchHealthCheckService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var healthChecks = await _watchHealthCheckService.GetAllAsync(stoppingToken);

            foreach (var healthCheck in healthChecks)
            {
                StartOrUpdateTimer(healthCheck, stoppingToken);
            }

            await Task.Delay(TimeSpan.FromMinutes(2), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                var updatedHealthChecks = await _watchHealthCheckService.GetUpdatedAsync(stoppingToken);

                foreach (var healthCheck in updatedHealthChecks)
                {
                    StartOrUpdateTimer(healthCheck, stoppingToken);
                }

                foreach (var healthCheck in _serviceTimers.Where(st => !updatedHealthChecks.Select(s => s.Id).Contains(st.Key)))
                {
                    await healthCheck.Value.DisposeAsync();
                    _serviceTimers.Remove(healthCheck.Key);
                }

                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }

        public void StartOrUpdateTimer(WatchHealthCheckDTO watchHealthCheckRequest, CancellationToken stoppingToken)
        {
            if (_serviceTimers.ContainsKey(watchHealthCheckRequest.Id))
            {
                _serviceTimers[watchHealthCheckRequest.Id].Dispose();
                _serviceTimers.Remove(watchHealthCheckRequest.Id);
            }
            else
            {
                var timer = new Timer(async _ => await _watchHealthCheckService.CheckServiceHealthAsync(watchHealthCheckRequest, stoppingToken),
                                      null,
                                      TimeSpan.FromSeconds(0),
                                      TimeSpan.FromSeconds(watchHealthCheckRequest.PollingIntervalInSeconds));
                _serviceTimers[watchHealthCheckRequest.Id] = timer;
            }
        }
    }
}
