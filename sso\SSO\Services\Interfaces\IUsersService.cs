﻿using SSO.Common.DTOs;
using SSO.Common.Responses;

namespace SSO.Services.Interfaces
{
    public interface IUsersService
    {
        Task<List<UserPermissionsDTO>> GetUserPermissionsAsync(Guid userId);

        Task<AddSupportChatPermissionsResult> AddSupportChatPermissionsAsync(UserDTO userDTO);

        Task<List<UserDTO>> GetUsersByPermissionAsync(string permission);

        Task<UserDTO> GetUserByEmailAsync(string email);

        Task<List<string>> GetUserCompanyPermissionsAsync(Guid userId, int companyId);
    }
}
