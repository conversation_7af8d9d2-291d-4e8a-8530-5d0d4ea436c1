﻿using AutoMapper;
using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Services.Interfaces;
using WatchTower.Database.Repositorise.Interfaces;

namespace WatchTower.Api.Services
{
    public class HealthCheckService : IHealthCheckService
    {
        private readonly IHealthCheckRepository _healthCheckRepository;
        private readonly IMapper _mapper;

        public HealthCheckService(IHealthCheckRepository healthCheckRepository, IMapper mapper)
        {
            _healthCheckRepository = healthCheckRepository;
            _mapper = mapper;
        }

        public Task CleanAsync(CancellationToken cancellationToken)
            => _healthCheckRepository.CleanAsync(cancellationToken);

        public async Task<List<HealthCheckDTO>> GetAllAsync(CancellationToken cancellationToken)
            => _mapper.Map<List<HealthCheckDTO>>(await _healthCheckRepository.GetAllAsync(cancellationToken));
    }
}
