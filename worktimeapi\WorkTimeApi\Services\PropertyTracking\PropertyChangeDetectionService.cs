using Newtonsoft.Json;
using System.Reflection;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Services.Interfaces.PropertyTracking;

namespace WorkTimeApi.Services.PropertyTracking
{
    public class PropertyChangeDetectionService : IPropertyChangeDetectionService
    {
        private readonly JsonSerializerSettings _jsonSettings;

        public PropertyChangeDetectionService()
        {
            _jsonSettings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                NullValueHandling = NullValueHandling.Include
            };
        }

        public List<EmployeePropertyEdit> DetectChanges<T>(T originalObject, T modifiedObject, Guid employeeId, Guid editorId, EditSource editSource, string objectName, Guid objectId) where T : class
        {
            var changes = new List<EmployeePropertyEdit>();

            if (originalObject == null || modifiedObject == null)
                return changes;

            var simpleProperties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite && IsSimpleProperty(p))
                .ToList();

            foreach (var property in simpleProperties)
            {
                var originalValue = property.GetValue(originalObject);
                var modifiedValue = property.GetValue(modifiedObject);

                if (!AreValuesEqual(originalValue, modifiedValue))
                {
                    var propertyEdit = new EmployeePropertyEdit
                    {
                        Id = Guid.NewGuid(),
                        EmployeeId = employeeId,
                        EditorId = editorId,
                        EditSource = editSource,
                        EditStatus = EditStatus.Pending,
                        ObjectName = objectName,
                        ObjectId = objectId,
                        PropertyName = property.Name,
                        OldValue = SerializeValue(originalValue),
                        NewValue = SerializeValue(modifiedValue),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    changes.Add(propertyEdit);
                }
            }

            return changes;
        }

        public List<EmployeePropertyEdit> DetectEmployeeWithSubObjectChanges(Employee originalEmployee, Employee modifiedEmployee, Guid editorId, EditSource editSource)
        {
            var allChanges = new List<EmployeePropertyEdit>();

            var employeeChanges = DetectChanges(originalEmployee, modifiedEmployee, originalEmployee.Id, editorId, editSource, "Employee", originalEmployee.Id);
            allChanges.AddRange(employeeChanges);

            var addressChanges = DetectAddressChanges(originalEmployee, modifiedEmployee, editorId, editSource);
            allChanges.AddRange(addressChanges);

            var bankAccountChanges = DetectBankAccountChanges(originalEmployee, modifiedEmployee, editorId, editSource);
            allChanges.AddRange(bankAccountChanges);

            return allChanges;
        }

        private bool IsSimpleProperty(PropertyInfo property)
        {
            var propertyType = property.PropertyType;

            return propertyType.IsPrimitive
                || propertyType == typeof(string)
                || propertyType == typeof(DateTime)
                || propertyType == typeof(DateTime?)
                || propertyType == typeof(Guid)
                || propertyType == typeof(Guid?)
                || propertyType == typeof(decimal)
                || propertyType == typeof(decimal?)
                || propertyType.IsEnum
                || (propertyType.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>) && propertyType.GetGenericArguments()[0].IsEnum);
        }

        private bool AreValuesEqual(object? value1, object? value2)
        {
            if ((value1 == null && value2 == null)
                || (value1 is string str1
                    && value2 is string str2
                    && string.IsNullOrEmpty(str1) && string.IsNullOrEmpty(str2)))
                return true;

            if (value1 == null || value2 == null)
                return false;

            if (value1 is DateTime dt1 && value2 is DateTime dt2)
            {
                return Math.Abs((dt1 - dt2).TotalMilliseconds) < 1000;
            }

            if (value1.GetType() == typeof(DateTime?) && value2.GetType() == typeof(DateTime?))
            {
                var ndt1 = (DateTime?)value1;
                var ndt2 = (DateTime?)value2;

                if (!ndt1.HasValue && !ndt2.HasValue)
                    return true;

                if (!ndt1.HasValue || !ndt2.HasValue)
                    return false;

                return Math.Abs((ndt1.Value - ndt2.Value).TotalMilliseconds) < 1000;
            }

            return value1.Equals(value2);
        }

        private string? SerializeValue(object? value)
        {
            if (value == null)
                return null;

            if (value is string || value.GetType().IsPrimitive || value is DateTime || value is DateTime? || value is Guid || value is decimal)
            {
                return value.ToString();
            }

            return JsonConvert.SerializeObject(value, _jsonSettings);
        }

        private List<EmployeePropertyEdit> DetectAddressChanges(Employee originalEmployee, Employee modifiedEmployee, Guid editorId, EditSource editSource)
        {
            var changes = new List<EmployeePropertyEdit>();

            var originalAddresses = originalEmployee.EmployeeAddresses?.ToDictionary(ea => ea.AddressId, ea => ea.Address) ?? new Dictionary<Guid, Address>();
            var modifiedAddresses = modifiedEmployee.EmployeeAddresses?.ToDictionary(ea => ea.AddressId, ea => ea.Address) ?? new Dictionary<Guid, Address>();

            foreach (var modifiedPair in modifiedAddresses)
            {
                var addressId = modifiedPair.Key;
                var modifiedAddress = modifiedPair.Value;

                if (originalAddresses.TryGetValue(addressId, out var originalAddress))
                {
                    var addressChanges = DetectChanges(originalAddress, modifiedAddress, originalEmployee.Id, editorId, editSource, "Address", addressId);
                    changes.AddRange(addressChanges);
                }
            }

            return changes;
        }

        private List<EmployeePropertyEdit> DetectBankAccountChanges(Employee originalEmployee, Employee modifiedEmployee, Guid editorId, EditSource editSource)
        {
            var changes = new List<EmployeePropertyEdit>();

            var originalBankAccounts = originalEmployee.EmployeeBankAccounts?.ToDictionary(eba => eba.BankAccountId, eba => eba.BankAccount) ?? new Dictionary<Guid, BankAccount>();
            var modifiedBankAccounts = modifiedEmployee.EmployeeBankAccounts?.ToDictionary(eba => eba.BankAccountId, eba => eba.BankAccount) ?? new Dictionary<Guid, BankAccount>();

            foreach (var modifiedPair in modifiedBankAccounts)
            {
                var bankAccountId = modifiedPair.Key;
                var modifiedBankAccount = modifiedPair.Value;

                if (originalBankAccounts.TryGetValue(bankAccountId, out var originalBankAccount))
                {
                    var bankAccountChanges = DetectChanges(originalBankAccount, modifiedBankAccount, originalEmployee.Id, editorId, editSource, "BankAccount", bankAccountId);
                    changes.AddRange(bankAccountChanges);
                }
            }

            return changes;
        }
    }
}
