﻿using Gateway.Common.Globals;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using System.Net;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class AddNewEmployeePayrollHandler(IWorkTimeApiConnection workTimeApiConnection,
        IUserRegistrationConnection userRegistrationConnection,
        GlobalEmployee globalEmployee) : IRequestHandler<AddNewEmployeePayrollRequest, IResult>
    {
        public async Task<IResult> Handle(AddNewEmployeePayrollRequest request, CancellationToken cancellationToken)
        {
            if (globalEmployee.Permissions is null || !globalEmployee.Permissions.Contains(DefaultPermissions.Employees.Write))
            {
                return Results.Unauthorized();
            }

            if (request.CompanyId == Guid.Empty)
            {
                return Results.BadRequest();
            }

            if (string.IsNullOrEmpty(request.PersonalData?.Email))
            {
                var user = new UserDTO()
                {
                    FirstName = request.PersonalData?.FirstName,
                    SecondName = request.PersonalData?.SecondName,
                    LastName = request.PersonalData?.LastName,
                    Password = request.PersonalData?.EGN
                };

                var registrationResponse = await userRegistrationConnection.RegisterUserByCodeAsync(user);
                if (registrationResponse is null || !registrationResponse.IsSuccessStatusCode)
                    return Results.BadRequest();

                var userDTO = await registrationResponse.Content.ReadFromJsonAsync<UserDTO>();
                if (userDTO is null)
                    return Results.BadRequest();

                request.NewUserId = Guid.Parse(userDTO.Id);
                request.PersonalData.Code = userDTO.Code;
                request.PersonalData.CodePassword = userDTO.Password;
            }
            else
            {
                var userResponse = await userRegistrationConnection.GetSenderaUserAsync(request.PersonalData?.Email);
                if (userResponse.IsSuccessStatusCode && userResponse.StatusCode != HttpStatusCode.NoContent)
                {
                    var userDTO = await userResponse.Content.ReadFromJsonAsync<UserDTO>(cancellationToken);
                    request.NewUserId = Guid.Parse(userDTO.Id);
                }
                else
                {
                    var user = new UserDTO()
                    {
                        FirstName = request.PersonalData?.FirstName,
                        SecondName = request.PersonalData?.SecondName,
                        LastName = request.PersonalData?.LastName,
                        Password = request.PersonalData?.EGN
                    };

                    var registrationResponse = await userRegistrationConnection.ConfirmedRegisterEmployeeByEmailAsync(user);
                    if (registrationResponse is null || !registrationResponse.IsSuccessStatusCode)
                        return Results.BadRequest();

                    var userDTO = await registrationResponse.Content.ReadFromJsonAsync<UserDTO>();
                    if (userDTO is null)
                        return Results.BadRequest();

                    request.NewUserId = Guid.Parse(userDTO.Id);
                    request.PersonalData.CodePassword = userDTO.Password;
                }
            }

            var newEmployeeResponse = await workTimeApiConnection.AddNewEmployeePayrollAsync(request);

            return new HttpResponseMessageResult(newEmployeeResponse);
        }
    }
}
