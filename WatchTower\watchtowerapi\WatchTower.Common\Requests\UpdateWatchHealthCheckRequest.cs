﻿using Gateway.Common.Requests;

namespace WatchTower.Common.Requests
{
    public class UpdateWatchHealthCheckRequest : BaseRequest
    {
        public Guid Id { get; set; }

        public required string Name { get; set; }

        public required string Environment { get; set; }

        public required string Url { get; set; }

        public bool IsUpdated { get; set; } = true;

        public required int PollingIntervalInSeconds { get; set; }
    }
}
