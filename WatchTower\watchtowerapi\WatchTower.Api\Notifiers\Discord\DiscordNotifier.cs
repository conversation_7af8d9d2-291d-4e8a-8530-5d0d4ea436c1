﻿using Discord;
using Discord.WebSocket;
using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Services.Interfaces.Notifiers;

namespace WatchTower.Api.Services.Notifiers
{
    public class DiscordNotifier : IDiscordNotifier
    {
        private readonly string _token;
        private readonly string _channelName;
        private readonly ulong _serverId;
        private readonly ILogger<DiscordNotifier> _logger;
        private DiscordSocketClient _client;

        public DiscordNotifier(IConfiguration configuration, ILogger<DiscordNotifier> logger)
        {
            _token = configuration["Discord:BotToken"] ?? "";
            ulong.TryParse(configuration["Discord:MicroinvestServerId"], out _serverId);
            _channelName = "-watch-tower-notifications";
            _client = new DiscordSocketClient();

            InitializeAsync().GetAwaiter().GetResult();
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            var readyTask = new TaskCompletionSource<bool>();

            _client.Ready += () =>
            {
                readyTask.SetResult(true);
                return Task.CompletedTask;
            };

            await _client.LoginAsync(TokenType.Bot, _token);
            await _client.StartAsync();

            // Wait until the client is ready
            await readyTask.Task;
        }

        private SocketGuild GetGuild(ulong guildId)
        {
            return _client.GetGuild(guildId);
        }

        private async Task<SocketTextChannel?> GetOrCreateChannelByName(ulong guildId, string channelName, string serviceName, string environment)
        {
            var channelWholeName = SanitizeChannelName($"{serviceName}-{environment}{channelName}");
            var guild = GetGuild(guildId);
            if (guild is null)
                return null;

            var textChannels = guild.TextChannels;

            var existingChannel = textChannels?.FirstOrDefault(channel => channel.Name.Equals(channelWholeName, StringComparison.OrdinalIgnoreCase));

            if (existingChannel != null)
            {
                return existingChannel;
            }

            var role = await GetOrCreateRoleByName(guild, serviceName);
            if (role is null)
                return null;

            var everyoneRole = guild.EveryoneRole;
            var specificRole = guild.GetRole(role.Id);

            var overwrites = new OverwritePermissions(
                readMessageHistory: PermValue.Deny,
                sendMessages: PermValue.Deny,
                viewChannel: PermValue.Deny
            );

            var roleOverwrites = new OverwritePermissions(
                readMessageHistory: PermValue.Allow,
                sendMessages: PermValue.Allow,
                viewChannel: PermValue.Allow
            );

            await guild.CreateTextChannelAsync(channelWholeName, properties =>
            {
                properties.PermissionOverwrites = new List<Overwrite>
                {
                    new Overwrite(everyoneRole.Id, PermissionTarget.Role, overwrites),
                    new Overwrite(specificRole.Id, PermissionTarget.Role, roleOverwrites)
                };
            });

            return guild.TextChannels.FirstOrDefault(channel => channel.Name.Equals(channelWholeName, StringComparison.OrdinalIgnoreCase));
        }

        private async Task<SocketRole?> GetOrCreateRoleByName(SocketGuild guild, string roleName, Color roleColor = default)
        {
            var role = guild.Roles.FirstOrDefault(r => r.Name.Equals(roleName, StringComparison.OrdinalIgnoreCase));

            if (role == null)
                await guild.CreateRoleAsync(roleName, null, roleColor, false, false);

            return guild.Roles.FirstOrDefault(r => r.Name.Equals(roleName, StringComparison.OrdinalIgnoreCase));
        }

        public async Task NotifyOnHealthCheckAddedAsync(string serviceName, HealthCheckDTO healthCheckDTO)
        {
            var message = healthCheckDTO.Status == "Unhealthy"
                ? $"Health check has failed for service: ***{serviceName}***\n\n"
                : $"Health check is successful for service: ***{serviceName}***\n\n";

            string formattedMessage = $"{message}" +
                          $"**Status**: {(healthCheckDTO.Status == "Unhealthy" ? "\uD83D\uDD34" : "\uD83D\uDFE2")} {healthCheckDTO.Status}\n" +
                          $"**Environment**: 🔧 {healthCheckDTO.Environment}\n" +
                          $"**CreatedDate**: ⏰ {healthCheckDTO.CreatedDate}\n" +
                          $"**WatchHealthCheckId**: **{healthCheckDTO.WatchHealthCheckId}**\n";

            var channel = await GetOrCreateChannelByName(_serverId, _channelName, serviceName, healthCheckDTO.Environment ?? "Unknown");
            if (channel is null)
            {
                _logger.LogError("Discord notifier is not working!");
                return;
            }

            await channel.SendMessageAsync(formattedMessage);
        }

        public async Task NotifyOnServicesStopedAsync(string serviceName, ServerDTO serverDTO)
        {
            string? message = serverDTO.IsFailing
                ? $"***{serviceName}*** times out!\n\n"
                : serverDTO.IsRestarted
                    ? $"Application pool: {serverDTO.AppPoolName} on server ***{serverDTO.Name}*** has been recycled successfully!\n\n"
                    : $"Application pool: {serverDTO.AppPoolName} on server ***{serverDTO.Name}*** couldn't be recycled!\n\n";

            var channel = await GetOrCreateChannelByName(_serverId, _channelName, serviceName, serverDTO.Name);
            if (channel is null)
            {
                _logger.LogError("Discord notifier is not working!");
                return;
            }

            await channel.SendMessageAsync(message);
        }

        private static string SanitizeChannelName(string channelName)
        {
            // Replace spaces with hyphens and remove invalid characters
            return new string(channelName
                .Replace(" ", "-")
                .ToLower()
                .Where(c => char.IsLetterOrDigit(c) || c == '-' || c == '_')
                .ToArray());
        }
    }
}
