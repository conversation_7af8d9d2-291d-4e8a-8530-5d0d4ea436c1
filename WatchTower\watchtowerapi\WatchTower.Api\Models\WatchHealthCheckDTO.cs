﻿using WatchTower.Api.Models.DTOs;

namespace WatchTower.Models
{
    public class WatchHealthCheckDTO
    {
        public Guid Id { get; set; }

        public required string Name { get; set; }

        public required string Environment { get; set; }

        public string Status { get; set; } = "";

        public required string Url { get; set; }

        public int PollingIntervalInSeconds { get; set; }

        public int FailedChecksCountToNotify { get; set; } = 5;

        public List<HealthCheckDTO> HealthChecks { get; set; } = new();
    }
}
