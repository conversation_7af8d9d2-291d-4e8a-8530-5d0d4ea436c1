﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
    <Configurations>Debug;Release;Testing</Configurations>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Discord.Net" Version="3.15.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="OpenSearch.Client" Version="1.7.1" />
    <PackageReference Include="OpenSearch.Net" Version="1.7.1" />
    <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.7.0-alpha.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
    <PackageReference Include="System.Management.Automation" Version="7.4.4" />
	<!-- Direct reference to Microsoft.CodeAnalysis.Common to resolve the version conflict -->
	<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.11.0" />
	<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.11.0" />
	<PackageReference Include="Microsoft.PowerShell.SDK" Version="7.4.4" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\WatchTower.Database\WatchTower.Database.csproj" />
  </ItemGroup>


  <ItemGroup Condition="'$(Configuration)'=='Debug'">
	  <ProjectReference Include="..\WatchTower.Common\WatchTower.Common.csproj" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)'!='Debug'">
	  <PackageReference Include="WatchTower.Common" Version="0.0.1-preview.20231123144352" />
  </ItemGroup>

</Project>
