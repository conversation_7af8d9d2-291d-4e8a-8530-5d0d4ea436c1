﻿using WatchTower.Models;
using WatchTower.Services.Interfaces;

namespace WatchTower.Api.Workers
{
    public class HealthChecksWorker : BackgroundService
    {
        private readonly IWatchHealthCheckService _watchHealthCheckService;
        private readonly Dictionary<Guid, CancellationTokenSource> _serviceTokens = new();
        private readonly Dictionary<Guid, Task> _runningTasks = new();

        public HealthChecksWorker(IWatchHealthCheckService watchHealthCheckService)
        {
            _watchHealthCheckService = watchHealthCheckService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var healthChecks = await _watchHealthCheckService.GetAllAsync(stoppingToken);

            foreach (var healthCheck in healthChecks)
            {
                StartOrUpdateTask(healthCheck);
            }

            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var updatedHealthChecks = await _watchHealthCheckService.GetUpdatedAsync(stoppingToken);

                    foreach (var healthCheck in updatedHealthChecks)
                    {
                        StartOrUpdateTask(healthCheck);
                    }

                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
                catch (Exception ex) 
                {
                    Console.WriteLine(ex);
                }
            }
        }

        public void StartOrUpdateTask(WatchHealthCheckDTO watchHealthCheckRequest)
        {
            if (_serviceTokens.ContainsKey(watchHealthCheckRequest.Id))
            {
                _serviceTokens[watchHealthCheckRequest.Id].Cancel();
                _serviceTokens.Remove(watchHealthCheckRequest.Id);
                _runningTasks.Remove(watchHealthCheckRequest.Id);
            }

            var cts = new CancellationTokenSource();
            var stoppingToken = cts.Token;
            var task = Task.Run(async () => await _watchHealthCheckService.CheckServiceHealthAsync(watchHealthCheckRequest, stoppingToken), stoppingToken);

            _serviceTokens[watchHealthCheckRequest.Id] = cts;
            _runningTasks[watchHealthCheckRequest.Id] = task;
        }
    }
}
