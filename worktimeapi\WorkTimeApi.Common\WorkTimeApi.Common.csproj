﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>1.0.4-preview.20250909155844</Version>
    <Configurations>Debug;Release;Docker</Configurations>
  </PropertyGroup>

  <ItemGroup Condition="'$(Configuration)'=='Debug'">
  	<ProjectReference Include="..\..\gateway\Gateway.Common\Gateway.Common.csproj" />
	<ProjectReference Include="..\..\sso\SSO.Common\SSO.Common.csproj" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)'!='Debug'">
    <PackageReference Include="Gateway.Common" Version="0.0.9-preview.20250902144032" />
	<PackageReference Include="SSO.Common" Version="0.0.8-preview.20250715114722" />
  </ItemGroup>
	
</Project>
