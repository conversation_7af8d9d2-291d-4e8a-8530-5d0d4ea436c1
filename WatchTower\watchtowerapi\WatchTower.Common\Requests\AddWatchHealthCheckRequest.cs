﻿using Gateway.Common.Requests;

namespace WatchTower.Common.Requests
{
    public class AddWatchHealthCheckRequest : BaseRequest
    {
        public Guid Id { get; set; }

        public required string Name { get; set; }

        public required string Environment { get; set; }

        public required string Url { get; set; }

        public bool IsUpdated { get; set; } = true;

        public required int PollingIntervalInSeconds { get; set; }

        public int FailedChecksCountToNotify { get; set; } = 5;
    }
}
