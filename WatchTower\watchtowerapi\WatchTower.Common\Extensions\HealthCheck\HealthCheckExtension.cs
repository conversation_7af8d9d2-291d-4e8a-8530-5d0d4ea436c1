﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace WatchTower.Common.Extensions.HealthCheck
{
    public static class HealthCheckExtension
    {
        public static WebApplication UseHealthChecks(this WebApplication app, string? pattern = null)
        {
            app.UseHealthChecks(pattern ?? "/_health", new HealthCheckOptions
            {
                ResponseWriter = async (context, report) =>
                {
                    context.Response.ContentType = "application/json";

                    var response = new
                    {
                        status = report.Status.ToString(),
                        entries = report.Entries.ToDictionary(entry => entry.Key, entry => new
                        {
                            data = entry.Value.Data,
                            description = entry.Value.Description,
                            duration = entry.Value.Duration.ToString(),
                            status = entry.Value.Status.ToString()
                        })
                    };

                    await context.Response.WriteAsync(JsonSerializer.Serialize(response));
                }
            });

            return app;
        }
    }
}
