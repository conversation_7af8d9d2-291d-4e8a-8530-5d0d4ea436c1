﻿using OpenSearch.Client;

namespace WatchTower.Api.Workers.Cleaners
{
    public class OpenSearchCleaner : BackgroundService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<OpenSearchCleaner> _logger;

        public OpenSearchCleaner(IConfiguration configuration, ILogger<OpenSearchCleaner> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var settings = new ConnectionSettings(new Uri(_configuration["OpenSearchUrl"] ?? ""))
                        .RequestTimeout(TimeSpan.FromMinutes(10))
                        .DisableDirectStreaming();
            var client = new OpenSearchClient(settings);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var getIndicesResponse = await client.Cat.IndicesAsync(ct: stoppingToken);
                    var records = getIndicesResponse.Records.Where(r => !r.Index.StartsWith('.'));

                    foreach (var index in records)
                    {
                        string indexName = index.Index;

                        var deleteInfoResponse = await client.DeleteByQueryAsync<dynamic>(d => d
                                        .Index(indexName)
                                        .Query(q => q
                                            .Bool(b => b
                                                .Must(m => m
                                                    .Term(t => t.Field("level").Value("info")),
                                                    m => m
                                                    .DateRange(r => r
                                                        .Field("@timestamp")
                                                        .LessThan("now-2h/h")
                                                    )
                                                )
                                            )
                                        ), stoppingToken);

                        var deleteErrorResponse = await client.DeleteByQueryAsync<dynamic>(d => d
                                        .Index(indexName)
                                        .Query(q => q
                                            .Bool(b => b
                                                .Must(m => m
                                                    .Term(t => t.Field("level").Value("error")),
                                                    m => m
                                                    .DateRange(r => r
                                                        .Field("@timestamp")
                                                        .LessThan("now-1d/d")
                                                    )
                                                )
                                            )
                                        ), stoppingToken);

                        // Log the results
                        if (deleteInfoResponse.Deleted > 0)
                            _logger.LogInformation("Info messages deleted successfully from {index}! Deleted messages {deletedMessages}", indexName, deleteInfoResponse.Deleted);
                        if (deleteErrorResponse.Deleted > 0)
                            _logger.LogInformation("Error messages deleted successfully from {index}! Deleted messages {deletedMessages}", indexName, deleteErrorResponse.Deleted);

                        if (deleteInfoResponse.Deleted == 0 && deleteErrorResponse.Deleted == 0)
                            _logger.LogWarning("No messages deleted from {index}!", indexName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Deleting old messages failed!");
                }
                finally
                {
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
        }
    }
}
