﻿using System.Management.Automation;
using WatchTower.Api.Services.Interfaces;

namespace WatchTower.Api.Services
{
    public class AppPoolRecycler : IAppPoolRecycler
    {
        private readonly ILogger<AppPoolRecycler> _logger;

        public AppPoolRecycler(ILogger<AppPoolRecycler> logger)
        {
            _logger = logger;
        }

        public async Task<bool> RecycleAppPoolAsync(string serverName, string username, string password, string appPoolName)
        {
            string script = $@"
                $password = ConvertTo-SecureString -AsPlainText -Force -String '{password}'
                
                $credentials = New-Object System.Management.Automation.PSCredential '{username}', $password
                
                $scriptBlock = {{ Import-Module WebAdministration; Restart-WebAppPool -Name ""{appPoolName}""}}
                
                Invoke-Command -ComputerName '{serverName}' -Credential $credentials -ScriptBlock $scriptBlock";

            using PowerShell ps = PowerShell.Create();
            ps.AddScript(script);
            try
            {
                var results = await ps.InvokeAsync();
                foreach (var result in results)
                {
                    _logger.LogInformation(result.ToString());
                }

                if (ps.Streams.Error.Count > 0)
                {
                    foreach (var error in ps.Streams.Error)
                    {
                        _logger.LogError("Error: {ErrorMessage}", error.ToString());
                    }
                }

                _logger.LogInformation("Application Pool {applicationPoolName} restarted successfully on {serverName}", appPoolName, serverName);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Failed to restart Application Pool {applicationPoolName} on {serverName} with {exception}", appPoolName, serverName, ex);

                return false;
            }
        }
    }
}
