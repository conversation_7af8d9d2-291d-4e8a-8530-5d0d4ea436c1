﻿using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Notifiers.Interfaces;
using WatchTower.Api.Services.Interfaces.Notifiers;

namespace WatchTower.Api.Notifiers
{
    public class Notify : INotify
    {
        private readonly List<INotifier> _notifiers;

        public Notify(IServiceProvider serviceProvider)
        {
            _notifiers = serviceProvider
                .GetServices<INotifier>()
                .Where(instance => instance != null)
                .ToList();
        }

        public async Task OnHealthCheckAdded(string serviceName, HealthCheckDTO healthCheckDTO)
        {
            foreach (var notifier in _notifiers)
                await notifier.NotifyOnHealthCheckAddedAsync(serviceName, healthCheckDTO);
        }
    }
}
