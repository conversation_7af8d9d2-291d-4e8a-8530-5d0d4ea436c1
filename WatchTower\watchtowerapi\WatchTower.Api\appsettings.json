{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Discord": {"BotToken": "MTE2ODUyMDQ5NzQ2NjcyNDM1Mw.Gofs0u.8TIih8-O-ytqpA55aIpMsAKZ-qIigFR1qEq9Ek", "MicroinvestServerId": "689797698584313917"}, "OpenSearchUrl": "http://*************:9200", "WebServicesManagement": {"ServerName": "webservices", "Username": "release", "Password": "Acy9zGMZm7q7IFb@", "AppPoolName": "UpdateServicePool"}}