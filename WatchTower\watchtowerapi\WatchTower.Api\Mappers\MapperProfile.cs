﻿using AutoMapper;
using WatchTower.Api.Models.DTOs;
using WatchTower.Common.Requests;
using WatchTower.Database.Models;
using WatchTower.Models;

namespace WatchTower.Mappers
{
    public class MapperProfile : Profile
    {
        public MapperProfile()
        {
            CreateMap<WatchHealthCheck, AddWatchHealthCheckRequest>()
                .ForMember(c => c.PollingIntervalInSeconds, src => src.MapFrom(r => r.PollingInterval.TotalSeconds))
                .ReverseMap()
                .ForMember(c => c.PollingInterval, src => src.MapFrom(r => TimeSpan.FromSeconds(r.PollingIntervalInSeconds)));

            CreateMap<WatchHealthCheck, UpdateWatchHealthCheckRequest>()
                .ForMember(c => c.PollingIntervalInSeconds, src => src.MapFrom(r => r.PollingInterval.TotalSeconds))
                .ReverseMap()
                .ForMember(c => c.PollingInterval, src => src.MapFrom(r => TimeSpan.FromSeconds(r.PollingIntervalInSeconds)));

            CreateMap<WatchHealthCheck, WatchHealthCheckDTO>()
                .ForMember(c => c.PollingIntervalInSeconds, src => src.MapFrom(r => r.PollingInterval.TotalSeconds))
                .ReverseMap()
                .ForMember(c => c.PollingInterval, src => src.MapFrom(r => TimeSpan.FromSeconds(r.PollingIntervalInSeconds)));

            CreateMap<WatchHealthCheckDTO, AddWatchHealthCheckRequest>().ReverseMap();
            CreateMap<WatchHealthCheckDTO, UpdateWatchHealthCheckRequest>().ReverseMap();

            CreateMap<HealthCheckDTO, HealthCheck>()
                .ForMember(dest => dest.Entries, opt => opt.MapFrom(src => src.Entries.Select(kvp => new HealthCheckEntry
                {
                    Key = kvp.Key,
                    Description = kvp.Value.Description,
                    Duration = kvp.Value.Duration,
                    Status = kvp.Value.Status,

                }).ToList()))
                .ReverseMap()
                .ForMember(dest => dest.Entries, opt => opt.MapFrom(src => src.Entries.ToDictionary(
                    entry => entry.Key,
                    entry => new HealthCheckEntryDTO
                    {
                        Description = entry.Description,
                        Duration = entry.Duration,
                        Status = entry.Status,
                    }
                    )));
        }
    }
}
