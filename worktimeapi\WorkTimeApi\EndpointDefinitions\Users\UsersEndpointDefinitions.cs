﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Users;
using WorkTimeApi.Connections;
using WorkTimeApi.Connections.Interfaces;
using WorkTimeApi.Database.Repositories.Interfaces.Users;
using WorkTimeApi.Database.Repositories.Users;
using WorkTimeApi.Extensions.MediatorExtensions;
using WorkTimeApi.Services.Interfaces.Users;
using WorkTimeApi.Services.Users;

namespace WorkTimeApi.EndpointDefinitions.Users
{
    public class UsersEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.MediatePost<UpdateUserHasSignedInRequest>("/users/update-has-signed-in")
                .MediateGet<GetUserRequest>("/users/{Id}")
                .AuthenticatedGet<GetUserProfileRequest>("/user")
                .MediateGet<LoadUserEmployeeRequest>("/user-employee/{userId}/{companyId}")
                .MediateGet<LoadUserEmployeePermissionsRequest>("/user-employee-permissions/{userId}/{companyId}")
                .MediateGet<LoadUserPermissionsRequest>("/user-permissions/{userId}/{companyId}")
                .MediatePost<AddUserWithEmployeeRequest>("/user-employee")
                .MediatePost<AddUserRequest>("/user")
                .MediatePost<UpdateUserEmailRequest>("/users/update-email")
                .MediateGet<ConfirmEmailCodeRequest>("/users/confirm-email-by-worktime-code");
        }

        public void DefineServices(IServiceCollection services)
        {
            services
                .AddScoped<IUserRepository, UserRepository>()
                .AddScoped<IUserService, UserService>()
                .AddHttpClient<IEmailsConnection, EmailsConnection>();
        }
    }
}
