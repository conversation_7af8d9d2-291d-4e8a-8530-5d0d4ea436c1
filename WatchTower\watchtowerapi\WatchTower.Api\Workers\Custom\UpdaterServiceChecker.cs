﻿
using WatchTower.Api.Models.DTOs;
using WatchTower.Api.Notifiers.Interfaces;
using WatchTower.Api.Services.Interfaces;
namespace WatchTower.Api.Workers.Custom
{
    public class UpdaterServiceChecker : BackgroundService
    {
        private readonly ILogger<UpdaterServiceChecker> _logger;
        private readonly IAppPoolRecycler _appPoolRecycler;
        private readonly IConfiguration _configuration;
        private readonly INotifier _notifier;

        private const string SERVICE_NAME = "UpdateService";

        public UpdaterServiceChecker(ILogger<UpdaterServiceChecker> logger,
            IAppPoolRecycler appPoolRecycler,
            IConfiguration configuration,
            INotifier notifier)
        {
            _logger = logger;
            _appPoolRecycler = appPoolRecycler;
            _configuration = configuration;
            _notifier = notifier;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var url = "https://club.microinvest.net/UpdateService/Check";
                var client = new HttpClient
                {
                    Timeout = TimeSpan.FromMinutes(1)
                };

                // Add the custom header to the request
                client.DefaultRequestHeaders.Add("data", "rmpwJ1f9A+dqgcrIRQormC99KjAvQCwxLTZ7a2UuOUl7OQIBCQ");

                try
                {
                    // Send a GET request to the specified Uri
                    var response = await client.GetAsync(url);

                    // Ensure we received a successful response.
                    response.EnsureSuccessStatusCode();
                }
                catch (HttpRequestException e)
                {
                    _logger.LogError("Unexpected exception! {errorMessage}", e);
                }
                catch (TaskCanceledException e)
                {
                    _logger.LogError("UpdateService is timing out! {errorMessage}", e);

                    var serverName = _configuration["WebServicesManagement:ServerName"] ?? "'";
                    var username = _configuration["WebServicesManagement:Username"] ?? "";
                    var password = _configuration["WebServicesManagement:Password"] ?? "'";
                    var appPoolName = _configuration["WebServicesManagement:AppPoolName"] ?? "'";

                    await _notifier.NotifyOnServicesStopedAsync(SERVICE_NAME, new ServerDTO
                    {
                        AppPoolName = appPoolName,
                        Name = serverName,
                        IsFailing = true,
                    });

                    var isRestartedSuccessfully = await _appPoolRecycler.RecycleAppPoolAsync(serverName, username, password, appPoolName);

                    await _notifier.NotifyOnServicesStopedAsync(SERVICE_NAME, new ServerDTO
                    {
                        AppPoolName = appPoolName,
                        Name = serverName,
                        IsRestarted = isRestartedSuccessfully,
                    });
                }

                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
    }
}
