import { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";

import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import ApprovedUserEditIcon from "../../../components/ApproveEdit/ApprovedUserEditIcon";
import ApproveEditCardHoverIcon from "../../../components/ApproveEdit/ApproveEditCardHoverIcon";
import PendingUserEditIcon from "../../../components/ApproveEdit/PendingUserEditIcon";
import ViewEditCardHoverIcon from "../../../components/ApproveEdit/ViewEditCardHoverIcon";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { ApproveEmployeePropertyEditDTO } from "../../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeePropertyEditDTO } from "../../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import {
  EditSource,
  EditStatus,
} from "../../../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";
import {
  approveEmployeePropertyEdit,
  declineEmployeePropertyEdit,
  deleteEmployeePropertyEdit,
} from "../../../services/employees/employeesService";
import { PropertyEditService } from "../../../services/employees/propertyEditService";
import Translator, { translate } from "../../../services/language/Translator";
import { useDefaultPlaces } from "../../DefaultLocationDataContext";
import { useMenu } from "../../MenuContext";

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin: 0.5rem 0;
  width: 100%;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 0;
  height: 4rem;
  width: 4rem;
`;

const EmployeeName = styled(Label)`
  text-align: left;
  font-weight: bold;
  opacity: 1;
  font-size: 1rem;
  word-wrap: normal;
  margin: 0;
`;

const DepartmentName = styled(Label)`
  font-size: 1rem;
  text-align: left;
  margin: 0;
  color: var(--profile-department-name-font-color);
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  flex: 1;
  height: 100%;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
    height: auto;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0.5rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 30%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 70%;
`;

const EditButton = styled(Button)<{
  $normalImage: string;
  $hoverImage: string;
  $isDisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.$normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.$hoverImage});
  }
`;

const TopContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  gap: 1rem;
`;

const EmployeeInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
`;

const RightColumn = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 17%;
  height: 100%;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  border-radius: 2.2rem;
  justify-content: flex-start;
  align-items: flex-start;
`;

const MenuText = styled(Label)<{ $isSelected?: boolean }>`
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  text-align: left;
  color: ${(props) =>
    props.$isSelected ? "var(--profile-menu-text-hover-color)" : "#bdc4d6"};
  cursor: pointer;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
`;

const MenuItem = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0.5rem;
  padding-top: 0;
  align-items: flex-start;

  &:hover {
    ${MenuText} {
      color: var(--profile-department-name-font-color);
    }
  }
`;

const MenuLine = styled.div`
  width: 100%;
  height: 1px;
  background-color: #bdc4d6;
  opacity: 0.5;
`;

const ContainersWrapper = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 2rem;
  align-items: flex-start;
`;

const MainContentWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 85%;
  gap: 1rem;
`;

const MainContent = styled(Container)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  width: 100%;
  height: 100%;
  column-gap: 2rem;
  @media (max-width: 1200px) {
    display: flex;
    flex-direction: column;
    height: auto;
  }
`;

const RightContainersWrapper = styled(Container)`
  display: flex;
  height: 100%;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
    height: auto;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 100%;
  }
`;

interface Props {
  profile: PersonalInformationDTO;
  setProfile: React.Dispatch<
    React.SetStateAction<PersonalInformationDTO | undefined>
  >;
  employeeName: string;
  shouldShowAdminEditButtons: boolean;
  shouldShowUserEditButtons: boolean;
}

const getAddressPurposeDescription = (
  purpose: number,
  addressName: string
): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Identity card";
    case AddressPurpose.ForContact:
      return "For contact";
    case AddressPurpose.ForRemoteWork:
      return "For remote work";
    case AddressPurpose.Abroad:
      return "Abroad";
    case AddressPurpose.Custom:
      return addressName;
    default:
      return "Unknown";
  }
};

const getAddressPurposeDescriptionGroupBox = (
  purpose: number,
  addressName: string
): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Address identity card";
    case AddressPurpose.ForContact:
      return "Address for contact";
    case AddressPurpose.ForRemoteWork:
      return "Address for remote work";
    case AddressPurpose.Abroad:
      return "Address abroad";
    case AddressPurpose.Custom:
      return addressName;
    default:
      return "Address unknown";
  }
};

function formatFullAddress(address?: AddressDTO | null) {
  if (!address) return "";
  const parts = [];
  if (address.neighborhood) parts.push(address.neighborhood);
  if (address.street) parts.push(address.street);
  if (address.block) parts.push(address.block);
  if (address.apartment) parts.push(address.apartment);
  return parts.join(", ");
}

const Addresses = ({
  profile,
  setProfile,
  employeeName,
  shouldShowAdminEditButtons,
  shouldShowUserEditButtons,
}: Props) => {
  const addressesList = profile.addresses;
  const employeeId = profile.employee.workTimeId;
  const [addresses, setAddresses] = useState<AddressDTO[] | null>();
  const [selectedAddress, setSelectedAddress] = useState<AddressDTO | null>();
  const { toggleMenu, changeView, registerCallback, unregisterCallback } =
    useMenu();
  const { countries } = useDefaultPlaces();

  const hasAddressPropertyEdit = (
    addressId: string,
    propertyName: string
  ): boolean => {
    return PropertyEditService.hasEditForProperty(
      profile.employeePropertyEdits || [],
      "Address",
      addressId,
      propertyName
    );
  };

  const getAddressOldValue = (addressId: string, propertyName: string): any => {
    const oldValue = PropertyEditService.getOldValueForProperty(
      profile.employeePropertyEdits || [],
      "Address",
      addressId,
      propertyName
    );
    try {
      return oldValue ? JSON.parse(oldValue) : oldValue;
    } catch {
      return oldValue;
    }
  };

  const getAddressPropertyEditInfo = (
    addressId: string,
    propertyName: string
  ) => {
    const edit = PropertyEditService.getEditForProperty(
      profile.employeePropertyEdits || [],
      "Address",
      addressId,
      propertyName
    );
    return edit;
  };

  const renderAddressUserEditIcon = (
    addressId: string,
    propertyName: string,
    previousValue?: string
  ) => {
    const editInfo = getAddressPropertyEditInfo(addressId, propertyName);
    if (!editInfo) return null;

    const handleDelete = async () => {
      try {
        await deleteEmployeePropertyEdit(editInfo.id);
        const updatedEdits = (profile.employeePropertyEdits || []).filter(
          (edit) => edit.id !== editInfo.id
        );
        setProfile({
          ...profile,
          employeePropertyEdits: updatedEdits,
        });
      } catch (error) {
        console.error("Failed to delete property edit:", error);
      }
    };

    if (editInfo.editSource === EditSource.AdminEdit) {
      return (
        <ViewEditCardHoverIcon
          previousValue={previousValue}
          onDelete={handleDelete}
        />
      );
    } else if (editInfo.editSource === EditSource.UserEdit) {
      if (editInfo.editStatus === EditStatus.Pending) {
        return <PendingUserEditIcon />;
      } else if (editInfo.editStatus === EditStatus.Approved) {
        return <ApprovedUserEditIcon onDelete={handleDelete} />;
      }
    }
    return null;
  };

  const handleApproveAddressProperty = async (
    addressId: string,
    propertyName: string
  ) => {
    const pendingEdit = PropertyEditService.getPendingEditForProperty(
      profile.employeePropertyEdits || [],
      "Address",
      addressId,
      propertyName
    );

    if (pendingEdit) {
      await approveEmployeePropertyEdit(
        new ApproveEmployeePropertyEditDTO({
          propertyEditId: pendingEdit.id,
          payrollId: profile.payrollPersonalData?.contractId,
          tabName: translate("Addresses"),
        })
      );

      const updatedEdits = (profile.employeePropertyEdits || []).filter(
        (edit) => edit.id !== pendingEdit.id
      );

      setProfile({
        ...profile,
        employeePropertyEdits: updatedEdits,
      });
    }
  };

  const handleDeclineAddressProperty = async (
    addressId: string,
    propertyName: string
  ) => {
    const pendingEdit = PropertyEditService.getPendingEditForProperty(
      profile.employeePropertyEdits || [],
      "Address",
      addressId,
      propertyName
    );

    if (pendingEdit) {
      await declineEmployeePropertyEdit(
        new DeclineEmployeePropertyEditDTO({
          propertyEditId: pendingEdit.id,
          payrollId: profile.payrollPersonalData?.contractId,
          tabName: translate("Addresses"),
        })
      );

      const updatedEdits = (profile.employeePropertyEdits || []).filter(
        (edit) => edit.id !== pendingEdit.id
      );

      setProfile({
        ...profile,
        employeePropertyEdits: updatedEdits,
      });
    }
  };

  useEffect(() => {
    setProfile(profile);
  }, [profile]);

  useEffect(() => {
    var filteredAddresses = profile.addresses.filter(
      (address: AddressDTO) =>
        address.purpose &&
        address.purpose.identifier !== AddressPurpose.IdentityCard
    );

    if (
      !filteredAddresses.some(
        (address: AddressDTO) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.ForContact
      )
    ) {
      const defaultAddress: AddressDTO = {
        id: "",
        employeeId: employeeId,
        city: { id: "", identifier: 0, name: "" },
        country: null,
        district: { id: "", identifier: 0, name: "" },
        municipality: { id: "", identifier: 0, name: "" },
        region: "",
        description: "",
        block: "",
        street: "",
        apartment: "",
        postalCode: "",
        neighborhood: "",
        phone: "",
        workPhone: "",
        email: "",
        workEmail: "",
        purpose: {
          id: "",
          identifier: AddressPurpose.ForContact,
          name: "For contact",
        },
        cityName: "",
        districtName: "",
        municipalityName: "",
      };

      filteredAddresses.push(defaultAddress);
    }

    filteredAddresses.sort((a, b) => {
      if (a.purpose?.identifier === AddressPurpose.ForContact) return -1;
      if (b.purpose?.identifier === AddressPurpose.ForContact) return 1;
      return 0;
    });

    setAddresses(filteredAddresses);
    setSelectedAddress(filteredAddresses[0]);
  }, [addressesList, employeeId]);

  useEffect(() => {
    const callbackId = registerCallback("addressAdded", (newAddress) => {
      if (addressesList && newAddress) {
        addressesList.push(newAddress.value);
        const updatedAddresses = addressesList.filter(
          (address: AddressDTO) =>
            address.purpose &&
            address.purpose.identifier !== AddressPurpose.IdentityCard
        );
        setAddresses(updatedAddresses);

        if (updatedAddresses.length === 1) {
          setSelectedAddress(updatedAddresses[0]);
        }
      }
    });

    return () => {
      unregisterCallback(callbackId);
    };
  }, [registerCallback, unregisterCallback, addressesList]);

  const handleAddressSelect = (address: AddressDTO) => {
    setSelectedAddress(address);
  };

  const handleAddNewAddress = () => {
    setSelectedAddress(null);
    changeView("new-address", "other", {
      employeeId,
      addressesList,
    });
    toggleMenu();
  };

  const handleEditAddress = () => {
    const step =
      selectedAddress?.id || `default_${selectedAddress?.purpose?.identifier}`;
    changeView("edit-employee-addresses", "other", {
      addressesList,
      employeeId,
      step,
    });
    toggleMenu();
  };

  return (
    <WrapperContainer>
      <ContainersWrapper>
        <MainContentWrapper>
          <TopContainer>
            <EmployeeImage
              src={profileMan}
              data-testid="profile-employee-image"
            />
            <EmployeeInfoContainer>
              <EmployeeName data-testid="profile-employee-name">
                {employeeName}
              </EmployeeName>
              <DepartmentName data-testid="profile-department-name">
                Department Name
              </DepartmentName>
            </EmployeeInfoContainer>
          </TopContainer>
          <MainContent>
            <LeftContainer data-testid="profile-left-container">
              <StyledFieldset
                onSubmit={(e) => e.preventDefault()}
                data-testid="current-address-fieldset"
              >
                <Legend data-testid="information-legend">
                  <Translator
                    getString={getAddressPurposeDescriptionGroupBox(
                      selectedAddress?.purpose?.identifier ?? 1,
                      selectedAddress?.description || ""
                    )}
                  />
                </Legend>
                <EditButton
                  data-testid="edit-address-button"
                  $normalImage={editIcon}
                  $hoverImage={editIconHover}
                  onClick={() => handleEditAddress()}
                  label=""
                  $isDisabled={true}
                />
                {selectedAddress?.purpose?.identifier ===
                AddressPurpose.Abroad ? (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Country</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "Country")
                          ) {
                            const oldCountryId = getAddressOldValue(
                              addressId,
                              "Country"
                            );
                            return countries.find(
                              (country) =>
                                country.identifier === Number(oldCountryId)
                            )?.name;
                          }
                          return countries.find(
                            (country) =>
                              country.identifier ===
                              Number(selectedAddress?.country)
                          )?.name;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "Country")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={
                              countries.find(
                                (country) =>
                                  country.identifier ===
                                  Number(selectedAddress?.country)
                              )?.name
                            }
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Country"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Country"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;

                            const previousValue = (() => {
                              const oldCountryId = getAddressOldValue(
                                addressId,
                                "Country"
                              );
                              return (
                                countries.find(
                                  (country) =>
                                    country.identifier === Number(oldCountryId)
                                )?.name || ""
                              );
                            })();

                            return renderAddressUserEditIcon(
                              addressId,
                              "Country",
                              previousValue
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                ) : (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>City</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            (hasAddressPropertyEdit(addressId, "CityName") ||
                              hasAddressPropertyEdit(addressId, "City"))
                          ) {
                            return (
                              getAddressOldValue(addressId, "CityName") ||
                              getAddressOldValue(addressId, "City")
                            );
                          }
                          return (
                            selectedAddress?.city?.name ??
                            selectedAddress?.cityName
                          );
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          (hasAddressPropertyEdit(addressId, "CityName") ||
                            hasAddressPropertyEdit(addressId, "City"))
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={
                              selectedAddress?.city?.name ??
                              selectedAddress?.cityName
                            }
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(addressId, "CityName")
                                ) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "CityName"
                                  );
                                }
                                if (hasAddressPropertyEdit(addressId, "City")) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "City"
                                  );
                                }
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(addressId, "CityName")
                                ) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "CityName"
                                  );
                                }
                                if (hasAddressPropertyEdit(addressId, "City")) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "City"
                                  );
                                }
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;

                            const cityNameEdit = getAddressPropertyEditInfo(
                              addressId,
                              "CityName"
                            );
                            const cityEdit = getAddressPropertyEditInfo(
                              addressId,
                              "City"
                            );
                            const editInfo = cityNameEdit || cityEdit;

                            if (!editInfo) return null;

                            const previousValue =
                              getAddressOldValue(addressId, "CityName") ||
                              getAddressOldValue(addressId, "City") ||
                              "";

                            return renderAddressUserEditIcon(
                              addressId,
                              editInfo.propertyName,
                              previousValue
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                )}

                {selectedAddress?.purpose?.identifier ===
                AddressPurpose.Abroad ? (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>City</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "CityName")
                          ) {
                            return getAddressOldValue(addressId, "CityName");
                          }
                          return selectedAddress?.cityName;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "CityName")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.cityName}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "CityName"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "CityName"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;

                            const previousValue =
                              getAddressOldValue(addressId, "CityName") || "";

                            return renderAddressUserEditIcon(
                              addressId,
                              "CityName",
                              previousValue
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                ) : (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>PC</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "PostalCode")
                          ) {
                            return getAddressOldValue(addressId, "PostalCode");
                          }
                          return selectedAddress?.postalCode;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "PostalCode")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.postalCode}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "PostalCode"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "PostalCode"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;
                            return renderAddressUserEditIcon(
                              addressId,
                              "PostalCode",
                              getAddressOldValue(addressId, "PostalCode") || ""
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                )}

                {selectedAddress?.purpose?.identifier !==
                  AddressPurpose.Abroad && (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>District</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            (hasAddressPropertyEdit(
                              addressId,
                              "DistrictName"
                            ) ||
                              hasAddressPropertyEdit(addressId, "District"))
                          ) {
                            return (
                              getAddressOldValue(addressId, "DistrictName") ||
                              getAddressOldValue(addressId, "District")
                            );
                          }
                          return (
                            selectedAddress?.district?.name ??
                            selectedAddress?.districtName
                          );
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          (hasAddressPropertyEdit(addressId, "DistrictName") ||
                            hasAddressPropertyEdit(addressId, "District"))
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={
                              selectedAddress?.district?.name ??
                              selectedAddress?.districtName
                            }
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "DistrictName"
                                  )
                                ) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "DistrictName"
                                  );
                                }
                                if (
                                  hasAddressPropertyEdit(addressId, "District")
                                ) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "District"
                                  );
                                }
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "DistrictName"
                                  )
                                ) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "DistrictName"
                                  );
                                }
                                if (
                                  hasAddressPropertyEdit(addressId, "District")
                                ) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "District"
                                  );
                                }
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;

                            const districtNameEdit = getAddressPropertyEditInfo(
                              addressId,
                              "DistrictName"
                            );
                            const districtEdit = getAddressPropertyEditInfo(
                              addressId,
                              "District"
                            );
                            const editInfo = districtNameEdit || districtEdit;

                            if (!editInfo) return null;

                            const previousValue =
                              getAddressOldValue(addressId, "DistrictName") ||
                              getAddressOldValue(addressId, "District") ||
                              "";

                            return renderAddressUserEditIcon(
                              addressId,
                              editInfo.propertyName,
                              previousValue
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                )}

                {selectedAddress?.purpose?.identifier !==
                  AddressPurpose.Abroad && (
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Municipality</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            (hasAddressPropertyEdit(
                              addressId,
                              "MunicipalityName"
                            ) ||
                              hasAddressPropertyEdit(addressId, "Municipality"))
                          ) {
                            return (
                              getAddressOldValue(
                                addressId,
                                "MunicipalityName"
                              ) || getAddressOldValue(addressId, "Municipality")
                            );
                          }
                          return (
                            selectedAddress?.municipality?.name ??
                            selectedAddress?.municipalityName
                          );
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          (hasAddressPropertyEdit(
                            addressId,
                            "MunicipalityName"
                          ) ||
                            hasAddressPropertyEdit(addressId, "Municipality"))
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={
                              selectedAddress?.municipality?.name ??
                              selectedAddress?.municipalityName
                            }
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "MunicipalityName"
                                  )
                                ) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "MunicipalityName"
                                  );
                                }
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "Municipality"
                                  )
                                ) {
                                  await handleApproveAddressProperty(
                                    addressId,
                                    "Municipality"
                                  );
                                }
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "MunicipalityName"
                                  )
                                ) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "MunicipalityName"
                                  );
                                }
                                if (
                                  hasAddressPropertyEdit(
                                    addressId,
                                    "Municipality"
                                  )
                                ) {
                                  await handleDeclineAddressProperty(
                                    addressId,
                                    "Municipality"
                                  );
                                }
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;

                            const municipalityNameEdit =
                              getAddressPropertyEditInfo(
                                addressId,
                                "MunicipalityName"
                              );
                            const municipalityEdit = getAddressPropertyEditInfo(
                              addressId,
                              "Municipality"
                            );
                            const editInfo =
                              municipalityNameEdit || municipalityEdit;

                            if (!editInfo) return null;

                            const previousValue =
                              getAddressOldValue(
                                addressId,
                                "MunicipalityName"
                              ) ||
                              getAddressOldValue(addressId, "Municipality") ||
                              "";

                            return renderAddressUserEditIcon(
                              addressId,
                              editInfo.propertyName,
                              previousValue
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                )}

                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>Address</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        if (
                          shouldShowAdminEditButtons &&
                          addressId &&
                          (hasAddressPropertyEdit(addressId, "Street") ||
                            hasAddressPropertyEdit(addressId, "Block") ||
                            hasAddressPropertyEdit(addressId, "Apartment") ||
                            hasAddressPropertyEdit(addressId, "Neighborhood"))
                        ) {
                          const virtualAddress = {
                            ...selectedAddress,
                            street:
                              getAddressOldValue(addressId, "Street") ||
                              selectedAddress?.street,
                            block:
                              getAddressOldValue(addressId, "Block") ||
                              selectedAddress?.block,
                            apartment:
                              getAddressOldValue(addressId, "Apartment") ||
                              selectedAddress?.apartment,
                            neighborhood:
                              getAddressOldValue(addressId, "Neighborhood") ||
                              selectedAddress?.neighborhood,
                          };
                          return formatFullAddress(virtualAddress);
                        }
                        return formatFullAddress(selectedAddress);
                      })()}
                    </ValueLabel>
                    {(() => {
                      const addressId = selectedAddress?.id;
                      return (
                        addressId &&
                        (hasAddressPropertyEdit(addressId, "Street") ||
                          hasAddressPropertyEdit(addressId, "Block") ||
                          hasAddressPropertyEdit(addressId, "Apartment") ||
                          hasAddressPropertyEdit(addressId, "Neighborhood"))
                      );
                    })() &&
                      (shouldShowAdminEditButtons ? (
                        <ApproveEditCardHoverIcon
                          newValue={formatFullAddress(selectedAddress)}
                          onConfirm={async () => {
                            const addressId = selectedAddress?.id;
                            if (addressId) {
                              if (hasAddressPropertyEdit(addressId, "Street")) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Street"
                                );
                              }
                              if (hasAddressPropertyEdit(addressId, "Block")) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Block"
                                );
                              }
                              if (
                                hasAddressPropertyEdit(addressId, "Apartment")
                              ) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Apartment"
                                );
                              }
                              if (
                                hasAddressPropertyEdit(
                                  addressId,
                                  "Neighborhood"
                                )
                              ) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Neighborhood"
                                );
                              }
                            }
                          }}
                          onCancel={async () => {
                            const addressId = selectedAddress?.id;
                            if (addressId) {
                              if (hasAddressPropertyEdit(addressId, "Street")) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Street"
                                );
                              }
                              if (hasAddressPropertyEdit(addressId, "Block")) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Block"
                                );
                              }
                              if (
                                hasAddressPropertyEdit(addressId, "Apartment")
                              ) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Apartment"
                                );
                              }
                              if (
                                hasAddressPropertyEdit(
                                  addressId,
                                  "Neighborhood"
                                )
                              ) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Neighborhood"
                                );
                              }
                            }
                          }}
                        />
                      ) : shouldShowUserEditButtons ? (
                        (() => {
                          const addressId = selectedAddress?.id;
                          if (!addressId) return null;

                          const streetEdit = getAddressPropertyEditInfo(
                            addressId,
                            "Street"
                          );
                          const blockEdit = getAddressPropertyEditInfo(
                            addressId,
                            "Block"
                          );
                          const apartmentEdit = getAddressPropertyEditInfo(
                            addressId,
                            "Apartment"
                          );
                          const neighborhoodEdit = getAddressPropertyEditInfo(
                            addressId,
                            "Neighborhood"
                          );
                          const editInfo =
                            streetEdit ||
                            blockEdit ||
                            apartmentEdit ||
                            neighborhoodEdit;

                          if (!editInfo) return null;

                          const virtualAddress = {
                            ...selectedAddress,
                            street:
                              getAddressOldValue(addressId, "Street") ||
                              selectedAddress?.street,
                            block:
                              getAddressOldValue(addressId, "Block") ||
                              selectedAddress?.block,
                            apartment:
                              getAddressOldValue(addressId, "Apartment") ||
                              selectedAddress?.apartment,
                            neighborhood:
                              getAddressOldValue(addressId, "Neighborhood") ||
                              selectedAddress?.neighborhood,
                          };
                          const previousValue =
                            formatFullAddress(virtualAddress);

                          return renderAddressUserEditIcon(
                            addressId,
                            editInfo.propertyName,
                            previousValue
                          );
                        })()
                      ) : null)}
                  </ValueColumn>
                </FieldsetRow>
              </StyledFieldset>
            </LeftContainer>
            <RightContainersWrapper>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">Phone number</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    $normalImage={editIcon}
                    $hoverImage={editIconHover}
                    onClick={() => handleEditAddress()}
                    label=""
                    $isDisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "Phone")
                          ) {
                            return getAddressOldValue(addressId, "Phone");
                          }
                          return selectedAddress?.phone;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "Phone")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.phone}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Phone"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Phone"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;
                            return renderAddressUserEditIcon(
                              addressId,
                              "Phone",
                              getAddressOldValue(addressId, "Phone") || ""
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "WorkPhone")
                          ) {
                            return getAddressOldValue(addressId, "WorkPhone");
                          }
                          return selectedAddress?.workPhone;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "WorkPhone")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.workPhone}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "WorkPhone"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "WorkPhone"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;
                            return renderAddressUserEditIcon(
                              addressId,
                              "WorkPhone",
                              getAddressOldValue(addressId, "WorkPhone") || ""
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">E-mail</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    $normalImage={editIcon}
                    $hoverImage={editIconHover}
                    onClick={() => handleEditAddress()}
                    label=""
                    $isDisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "Email")
                          ) {
                            return getAddressOldValue(addressId, "Email");
                          }
                          return selectedAddress?.email;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "Email")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.email}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "Email"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "Email"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;
                            return renderAddressUserEditIcon(
                              addressId,
                              "Email",
                              getAddressOldValue(addressId, "Email") || ""
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {(() => {
                          const addressId = selectedAddress?.id;

                          if (
                            shouldShowAdminEditButtons &&
                            addressId &&
                            hasAddressPropertyEdit(addressId, "WorkEmail")
                          ) {
                            return getAddressOldValue(addressId, "WorkEmail");
                          }
                          return selectedAddress?.workEmail;
                        })()}
                      </ValueLabel>
                      {(() => {
                        const addressId = selectedAddress?.id;
                        return (
                          addressId &&
                          hasAddressPropertyEdit(addressId, "WorkEmail")
                        );
                      })() &&
                        (shouldShowAdminEditButtons ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.workEmail}
                            onConfirm={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleApproveAddressProperty(
                                  addressId,
                                  "WorkEmail"
                                );
                              }
                            }}
                            onCancel={async () => {
                              const addressId = selectedAddress?.id;
                              if (addressId) {
                                await handleDeclineAddressProperty(
                                  addressId,
                                  "WorkEmail"
                                );
                              }
                            }}
                          />
                        ) : shouldShowUserEditButtons ? (
                          (() => {
                            const addressId = selectedAddress?.id;
                            if (!addressId) return null;
                            return renderAddressUserEditIcon(
                              addressId,
                              "WorkEmail",
                              getAddressOldValue(addressId, "WorkEmail") || ""
                            );
                          })()
                        ) : null)}
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
            </RightContainersWrapper>
          </MainContent>
        </MainContentWrapper>
        <RightColumn>
          {addresses &&
            addresses.map((address, index) => (
              <MenuItem
                key={index}
                onClick={() => handleAddressSelect(address)}
              >
                <MenuText $isSelected={selectedAddress === address}>
                  {getAddressPurposeDescription(
                    address.purpose.identifier,
                    address.description
                  )}
                </MenuText>
                <MenuLine />
              </MenuItem>
            ))}
          <MenuItem onClick={() => handleAddNewAddress()}>
            <MenuText>New address</MenuText>
          </MenuItem>
        </RightColumn>
      </ContainersWrapper>
    </WrapperContainer>
  );
};

export default Addresses;
