import { useEffect, useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../../app/hooks";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { Genders } from "../../../constants/enum";
import { LOCAL_STORAGE_COMPANY_ID } from "../../../constants/local-storage-constants";
import { AddressPurpose } from "../../../models/DTOs/address/AddressDTO";
import { EditEmployeeDTO } from "../../../models/DTOs/editEmployee/EditEmployeeDTO";
import { IdentityCardDataDTO } from "../../../models/DTOs/newEmployee/IdentityCardDataDTO";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import { PersonalDataDTO } from "../../../models/DTOs/newEmployee/PersonalDataDTO";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";
import { editEmployee } from "../../../services/employees/employeesService";
import { translate } from "../../../services/language/Translator";
import { parseDateString } from "../../../utils/dateUtils";
import { useMenu } from "../../MenuContext";
import { onEmployeeEdited } from "../employeesActions";
import NewIdentificationCardData from "../newEmployee/NewIdentificationCardData";
import NewPersonalData from "../newEmployee/NewPersonalData";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ isSelected: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem 0.8rem 1.8rem;
  text-align: left;
  background-color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.isSelected ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-top: 1rem;
`;

const NavigationContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
  justify-content: center;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 5;
`;

const NextButton = styled(Button)`
  width: 90%;
`;
export interface EditEmployeeFormData {
  personalData: FormValidationState;
  identityCardData: FormValidationState;
}
interface FormValidationState {
  isValid: boolean;
  data: any;
}

const EditEmployeeDataSideMenu = () => {
  const dispatch = useAppDispatch();
  const { toggleMenu, changeView, viewData } = useMenu();
  const [activeStep, setActiveStep] = useState(viewData?.step ?? 1);
  const [isLoading, setIsLoading] = useState(false);
  const [isPersonDataChanged, setIsPersonDataChanged] = useState(false);
  const [isIdentityDataChanged, setIsIdentityDataChanged] = useState(false);
  console.log(viewData);

  const convertEmployeeToPersonalData = (
    employeeData: PersonalInformationDTO
  ): PersonalDataDTO => {
    return {
      firstName: employeeData.employee.firstName || "",
      secondName: employeeData.employee.secondName || "",
      lastName: employeeData.employee.lastName || "",
      egn: employeeData.employee.egn || "",
      birthDate: employeeData.employee.birthDate
        ? parseDateString(employeeData.employee.birthDate)
        : undefined,
      contractType: employeeData.payrollPersonalData?.contract?.id ?? 0,
      birthPlace: employeeData.employee.birthPlace || "",
      iban: employeeData.iban || "",
      email: employeeData.employee.email || "",
    };
  };

  const convertEmployeeToIdentityCardData = (
    employeeData: PersonalInformationDTO
  ): IdentityCardDataDTO => {
    var address = employeeData.addresses.find(
      (address) => address.purpose.identifier === AddressPurpose.IdentityCard
    );
    return {
      idNumber: employeeData.employee.idNumber || "",
      issuedOn: employeeData.employee.idIssueDate
        ? parseDateString(employeeData.employee.idIssueDate)
        : undefined,
      issuedBy: employeeData.employee.idIssuedFrom || "",
      citizenship: employeeData.employee.citizenship || "",
      gender: employeeData.employee.gender || Genders.None,
      idAddress: {
        id: address?.id || undefined,
        employeeId: address?.employeeId || undefined,
        city: address?.city || null,
        postalCode: address?.postalCode || "",
        region: address?.region || "",
        municipality: address?.municipality || null,
        district: address?.district || null,
        street: address?.street || "",
        block: address?.block || "",
        apartment: address?.apartment || "",
        phone: address?.phone || "",
        workPhone: address?.workPhone || "",
        email: address?.email || "",
        workEmail: address?.workEmail || "",
        country: address?.country || null,
        purpose: AddressPurpose.IdentityCard,
        description: address?.description || "",
        neighborhood: address?.neighborhood || "",
        cityName: address?.cityName || "",
        districtName: address?.districtName || "",
        municipalityName: address?.municipalityName || "",
        countryName: address?.country?.name || "",
      } as NewAddressDTO,
    };
  };

  const profileData = viewData?.incomingProfile as PersonalInformationDTO;

  const [formData, setFormData] = useState<EditEmployeeFormData>({
    personalData: {
      isValid: false,
      data: profileData?.employee
        ? convertEmployeeToPersonalData(profileData)
        : undefined,
    },
    identityCardData: {
      isValid: false,
      data: profileData?.employee
        ? convertEmployeeToIdentityCardData(profileData)
        : undefined,
    },
  });

  useEffect(() => {
    if (profileData) {
      setFormData((prev) => ({
        ...prev,
        personalData: {
          isValid: false,
          data: convertEmployeeToPersonalData(profileData),
        },
        identityCardData: {
          isValid: false,
          data: convertEmployeeToIdentityCardData(profileData),
        },
      }));
    }
  }, [profileData]);

  const isCurrentStepValid = () => {
    switch (activeStep) {
      case 1:
        return formData.personalData.isValid;
      case 2:
        return formData.identityCardData.isValid;
      default:
        return false;
    }
  };

  const handlePersonalDataValidation = (
    isValid: boolean,
    data: PersonalDataDTO
  ) => {
    setFormData((prev) => ({
      ...prev,
      personalData: { isValid, data },
    }));
  };

  const handlePersonalDataChange = () => {
    setIsPersonDataChanged(true);
  };

  const handleIdentityDataChange = () => {
    setIsIdentityDataChanged(true);
  };

  const handleIdentificationDataValidation = (isValid: boolean, data: any) => {
    setFormData((prev) => ({
      ...prev,
      identityCardData: { isValid, data },
    }));
  };

  const steps = [
    {
      id: 1,
      label: translate("strPersonalData"),
      component: (
        <NewPersonalData
          onValidation={handlePersonalDataValidation}
          onChange={handlePersonalDataChange}
          data={formData.personalData.data}
        />
      ),
    },
    {
      id: 2,
      label: translate("strIDCardData"),
      component: (
        <NewIdentificationCardData
          onValidation={handleIdentificationDataValidation}
          onChange={handleIdentityDataChange}
          data={formData.identityCardData.data}
        />
      ),
    },
  ];

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId);
  };

  const handleSubmit = async () => {
    if (!isCurrentStepValid()) {
      return;
    }

    setIsLoading(true);
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    const editEmployeeData: EditEmployeeDTO = {
      employeeId: profileData?.employee.workTimeId,
      payrollId: profileData?.payrollPersonalData.id,
      companyId: companyId,
      personalData: formData.personalData.data as PersonalDataDTO,
      identityCardData: formData.identityCardData.data as IdentityCardDataDTO,
      addressForCorrespondence: null,
      addressForRemoteWork: null,
      addressForAbroad: null,
      otherAddresses: null,
    };

    const editedEmployee = await editEmployee(editEmployeeData);
    //to do да се dispatch-ва когато се удобри промяната
    dispatch(onEmployeeEdited(editedEmployee));

    setIsLoading(false);
    toggleMenu();
    changeView("employees", "other");
  };

  return (
    <MainContainer data-testid="edit-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            isSelected={activeStep === step.id}
            disabled={false}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        {steps.find((step) => step.id === activeStep)?.component}
      </ContentContainer>

      <NavigationContainer data-testid="navigation-container">
        {activeStep === steps.length ? (
          <NextButton
            label="Edit"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid() || !isIdentityDataChanged}
            data-testid="identity-data-button"
          />
        ) : (
          <NextButton
            label="Edit"
            onClick={handleSubmit}
            disabled={
              isLoading || !isCurrentStepValid() || !isPersonDataChanged
            }
            data-testid="personal-data-button"
          />
        )}
      </NavigationContainer>
    </MainContainer>
  );
};

export default EditEmployeeDataSideMenu;
