﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
	<Version>0.0.1-preview.20231123144352</Version>
	<Configurations>Debug;Release;Testing</Configurations>
  </PropertyGroup>

  <PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.7.0-alpha.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
	<PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="8.8.0" />
	<PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="8.1.0" />
	<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.GrpcNetClient" Version="1.6.0-beta.2" />
	<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.9.0" />
  </ItemGroup>

  <ItemGroup Condition="'$(Configuration)'=='Debug'">
	  <ProjectReference Include="..\..\..\gateway\Gateway.Common\Gateway.Common.csproj" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)'!='Debug'">
	  <PackageReference Include="Gateway.Common" Version="0.0.9-preview.20240826154920" />
  </ItemGroup>

</Project>
