﻿
using Microsoft.Extensions.Configuration;
using OpenSearch.Client;

namespace WatchTower.Api.Workers.Cleaners
{
    public class IndeciesFlusher : BackgroundService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<OpenSearchCleaner> _logger;

        public IndeciesFlusher(IConfiguration configuration, ILogger<OpenSearchCleaner> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var settings = new ConnectionSettings(new Uri(_configuration["OpenSearchUrl"] ?? ""))
                        .RequestTimeout(TimeSpan.FromMinutes(10))
                        .DisableDirectStreaming();
            var client = new OpenSearchClient(settings);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var flushResponse = await client.Indices.FlushAsync(Indices.All, ct: stoppingToken);

                    if (flushResponse.IsValid)
                    {
                        _logger.LogInformation("All indices flushed successfully.");
                    }
                    else
                    {
                        _logger.LogWarning("Failed to flush indices. Reason: {Reason}", flushResponse.ServerError?.Error?.Reason);
                    }
                }
                finally
                {
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
                }
            }
        }
    }
}
