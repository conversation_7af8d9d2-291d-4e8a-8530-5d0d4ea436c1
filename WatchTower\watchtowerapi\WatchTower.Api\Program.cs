using Gateway.Common.Extensions.LoggerExtensions;
using Gateway.Extenstions.EndpointExtensions;
using Microsoft.EntityFrameworkCore;
using WatchTower.Api.Workers.Cleaners;
using WatchTower.Database;
using WatchTower.Extenstions.MetricsExtensions;
using WatchTower.EndpointDefinitions.MapperExtensions;
using WatchTower.Common.Extensions.Aspire;

namespace WatchTower
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            builder.AddServiceDefaults();
            builder.Services.AddCors(p => p.AddPolicy("corsapp", builder =>
            {
                builder.WithOrigins("*").AllowAnyMethod().AllowAnyHeader().WithExposedHeaders("Authorization", "Refresh-Token");
            }));

            builder.Services.AddMediatR(x =>
            {
                x.Lifetime = ServiceLifetime.Scoped;
                x.RegisterServicesFromAssembly(typeof(Program).Assembly);
            });

            builder.Services.AddEndpointDefinitions(typeof(Program));
            builder.Services.AddOpenSearchLogging();
            builder.Services.InitializeAutoMapper();
            builder.Services.AddSignalR();

            builder.Services.AddHostedService<OpenSearchCleaner>();
            builder.Services.AddHostedService<IndeciesFlusher>();

            builder.Services.AddOpenTelemetryMetrics();

            builder.Services
                .AddDbContextFactory<WatchTowerDbContext>(
                    options => options.UseSqlServer(
                        builder.Configuration.GetConnectionString("DefaultConnection")));

            var app = builder.Build();

            app.MapDefaultEndpoints();
            app.MapPrometheusScrapingEndpoint();
            app.UseCors("corsapp");
            app.UseEndpointDefinitions();
            app.UseHttpsRedirection();
            app.UseMiddlewareExceptionLogging();

            var context = app.Services.GetRequiredService<IDbContextFactory<WatchTowerDbContext>>().CreateDbContext();
            context.Database.Migrate();

            app.Run();
        }
    }
}