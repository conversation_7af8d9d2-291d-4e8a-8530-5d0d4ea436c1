﻿using MediatR;
using WatchTower.Common.Requests;
using WatchTower.Services.Interfaces;

namespace WatchTower.Handlers
{
    public class AddWatchServiceHandler : IRequestHandler<AddWatchHealthCheckRequest, IResult>
    {
        private readonly IWatchHealthCheckService _watchHealthCheckService;

        public AddWatchServiceHandler(IWatchHealthCheckService watchHealthCheckService)
        {
            _watchHealthCheckService = watchHealthCheckService;
        }

        public async Task<IResult> Handle(AddWatchHealthCheckRequest request, CancellationToken cancellationToken)
        {
            var watchHealthCheckRequest = await _watchHealthCheckService.AddWatchHealthCheckServiceAsync(request, cancellationToken);

            return Results.Ok(watchHealthCheckRequest);
        }
    }
}
