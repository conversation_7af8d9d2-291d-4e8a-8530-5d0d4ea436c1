﻿using AutoMapper;
using Moq;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.TRZ;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Mappers;
using WorkTimeApi.Services.Interfaces.Addresses;
using WorkTimeApi.Services.Interfaces.Nomenclatures;

namespace WorkTimeApi.Tests.Mappers;

public class TRZMapperTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IAddressesService> _addressesServiceMock;
    private readonly Mock<IKidsService> _kidsServiceMock;
    private readonly TRZMapper _trzMapper;

    public TRZMapperTests()
    {
        _mapperMock = new Mock<IMapper>();
        _addressesServiceMock = new Mock<IAddressesService>();
        _kidsServiceMock = new Mock<IKidsService>();
        _trzMapper = new TRZMapper(_mapperMock.Object, _addressesServiceMock.Object, _kidsServiceMock.Object);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Map_Single_Employee_With_Single_Name()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John",
                EGN = "1234567890",
                WorkTimeId = worktimeId,
                EMail = "<EMAIL>",
                WorkPhone = "+1234567890",
                Phone = "+0987654321",
                IsForeigner = false,
                UserId = Guid.NewGuid()
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal("John", employee.FirstName);
        Assert.Empty(employee.SecondName);
        Assert.Empty(employee.LastName);
        Assert.Equal(companyId, employee.CompanyId);
        Assert.Equal(EmployeeCompanyStatus.PendingTRZ, employee.Status);
        Assert.Equal("Българско", employee.Citizenship);
        Assert.NotNull(employee.Addresses);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Map_Employee_With_Two_Names()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal("John", employee.FirstName);
        Assert.Empty(employee.SecondName);
        Assert.Equal("Doe", employee.LastName);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Map_Employee_With_Three_Names()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Michael Doe",
                WorkTimeId = worktimeId,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal("John", employee.FirstName);
        Assert.Equal("Michael", employee.SecondName);
        Assert.Equal("Doe", employee.LastName);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Map_Employee_With_More_Than_Three_Names()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Michael Robert Doe Smith",
                WorkTimeId = worktimeId,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal("John", employee.FirstName);
        Assert.Equal("Michael", employee.SecondName);
        Assert.Equal("Robert Doe Smith", employee.LastName);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Handle_Null_Or_Empty_Name()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = null,
                WorkTimeId = worktimeId,
                IsForeigner = false,

            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Null(employee.FirstName);
        Assert.Null(employee.SecondName);
        Assert.Null(employee.LastName);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Set_Foreigner_Citizenship_Empty()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                IsForeigner = true
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal(string.Empty, employee.Citizenship);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Handle_Null_WorkTimeId()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = null,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO();

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal(Guid.Empty, employee.WorkTimeId);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Handle_Null_UserId()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                UserId = null,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal(Guid.Empty, employee.UserId);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Map_Multiple_Employees()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = Guid.NewGuid(),
                IsForeigner = false
            },
            new()
            {
                TRZEmployeeId = 2,
                Name = "Jane Smith",
                WorkTimeId = Guid.NewGuid(),
                IsForeigner = true
            }
        };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns<TRZEmployeeDTO>(emp => new EmployeeDTO { WorkTimeId = emp.WorkTimeId ?? Guid.Empty });

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Equal(2, result.Count);

        var firstEmployee = result.First();
        Assert.Equal("John", firstEmployee.FirstName);
        Assert.Equal("Doe", firstEmployee.LastName);
        Assert.Equal("Българско", firstEmployee.Citizenship);

        var secondEmployee = result.Skip(1).First();
        Assert.Equal("Jane", secondEmployee.FirstName);
        Assert.Equal("Smith", secondEmployee.LastName);
        Assert.Equal(string.Empty, secondEmployee.Citizenship);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Set_Upload_Date_To_Current_Time()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var dateBefore = DateTime.Now.AddSeconds(-1);

        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);
        var dateAfter = DateTime.Now.AddSeconds(1);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.True(employee.UploadDate >= dateBefore && employee.UploadDate <= dateAfter);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Initialize_Empty_Addresses_Collection()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.NotNull(employee.Addresses);
        Assert.Empty(employee.Addresses);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Handle_Empty_Input_List()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>();

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void MapTRZEmployeesDTOToEmployeesDTO_Should_Copy_Phone_Property()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var phoneNumber = "+1234567890";

        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                WorkTimeId = worktimeId,
                Phone = phoneNumber,
                IsForeigner = false
            }
        };

        var mappedEmployee = new EmployeeDTO { WorkTimeId = worktimeId };

        _mapperMock.Setup(x => x.Map<EmployeeDTO>(It.IsAny<TRZEmployeeDTO>()))
            .Returns(mappedEmployee);

        // Act
        var result = _trzMapper.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId);

        // Assert
        Assert.Single(result);
        var employee = result.First();
        Assert.Equal(phoneNumber, employee.Phone);
    }
}