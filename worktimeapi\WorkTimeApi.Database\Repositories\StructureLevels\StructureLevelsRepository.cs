﻿using Microsoft.EntityFrameworkCore;
using System.Diagnostics.SymbolStore;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.StructureLevels;

namespace WorkTimeApi.Database.Repositories.StructureLevels
{
    public class StructureLevelsRepository : IStructureLevelsRepository
    {
        private readonly IDbContextFactory<WorkTimeApiDbContext> _contextFactory;

        public StructureLevelsRepository(IDbContextFactory<WorkTimeApiDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<StructureLevel> AddStructureLevelAsync(StructureLevel structureLevel)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            context.StructureLevels.Add(structureLevel);
            await context.SaveChangesAsync();

            return structureLevel;
        }

        public async Task<IEnumerable<StructureLevel>> AddStructureLevelsAsync(IEnumerable<StructureLevel> structureLevels)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            context.StructureLevels.AddRange(structureLevels);
            await context.SaveChangesAsync();

            return structureLevels;
        }

        public async Task<StructureLevel?> GetRootStructureLevelByCompanyIdAsync(Guid companyId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var structureLevels = await context.StructureLevels
                .Where(sl => sl.CompanyId == companyId).ToListAsync();

            structureLevels.RemoveAll(sl => sl.ParentStructureLevel is not null);

            return structureLevels?.FirstOrDefault();
        }

        public async Task<IEnumerable<StructureLevel>> GetStructureLevelsByCompanyIdAsync(Guid companyId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var structureLevelIds = await context.StructureLevels
                .Where(sl => sl.CompanyId == companyId)
                .ToListAsync();

            return structureLevelIds;
        }

        public async Task UpdateStructureLevelAsync(StructureLevel structureLevel)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            context.StructureLevels.Update(structureLevel);

            await context.SaveChangesAsync();
        }

        public async Task UpdateStructureLevelsAsync(IEnumerable<StructureLevel> structureLevels)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            foreach (var structureLevel in structureLevels)
            {
                context.StructureLevels.Update(structureLevel);
            }

            await context.SaveChangesAsync();
        }

        public async Task<bool> DeleteStructureLevelByIdAsync(Guid structureLevelId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var structureLevel = await context.StructureLevels.FindAsync(structureLevelId);

            if (structureLevel is null)
                return false;

            context.StructureLevels.Remove(structureLevel);
            await context.SaveChangesAsync();

            return true;
        }

        public async Task DeleteStructureLevelsAsync(IEnumerable<StructureLevel> structureLevels)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var ids = structureLevels.Select(s => s.Id);
            var st = context.StructureLevels.Where(st => ids.Contains(st.Id)).ToList();
            context.StructureLevels.RemoveRange(st);

            await context.SaveChangesAsync();
        }

        public async Task MovePayrollsFromDeletedStructureLevelsAsync(IEnumerable<StructureLevel> structureLevelsToDelete)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var deletedIds = structureLevelsToDelete.Select(l => l.Id).ToHashSet();

            foreach (var structureLevel in structureLevelsToDelete)
            {
                var level = await context.StructureLevels.Include(l => l.ParentStructureLevel).FirstOrDefaultAsync(l => l.Id == structureLevel.Id);

                if (level == null)
                    continue;

                var payrolls = await context.Payrolls.Where(p => p.StructureLevelId == level.Id).ToListAsync();
                var trzPayrolls = await context.TRZPayrolls.Where(p => p.StructureLevelId == level.Id).ToListAsync();

                if (payrolls.Count == 0 && trzPayrolls.Count == 0)
                    continue;

                foreach (var payroll in payrolls)
                {
                    var closestParent = level;

                    while (closestParent != null && deletedIds.Contains(closestParent.Id))
                    {
                        closestParent = closestParent.ParentStructureLevel;
                    }

                    if (closestParent != null)
                    {
                        payroll.StructureLevelId = closestParent.Id;
                    }
                }

                foreach (var payroll in trzPayrolls)
                {
                    var closestParent = level;

                    while (closestParent != null && deletedIds.Contains(closestParent.Id))
                    {
                        closestParent = closestParent.ParentStructureLevel;
                    }

                    if (closestParent != null)
                    {
                        payroll.StructureLevelId = closestParent.Id;
                    }
                }
            }

            await context.SaveChangesAsync();
        }
    }
}
