import {
  EventType,
  EventTypeDescriptions,
} from "../models/DTOs/absence/EventType";
import { translate } from "../services/language/Translator";

export interface AbsenceWithType {
  isHospital: boolean;
  typeIdentifier: EventType;
}

export const getLeaveType = (absence: AbsenceWithType): string => {
  return EventTypeDescriptions[absence.typeIdentifier] ?? "";
};

export const getLeaveDeletedMessage = (absence: AbsenceWithType): string => {
  if (absence.isHospital) {
    return absence.typeIdentifier === EventType.БолниченПоБременност
      ? translate("strMaternityLeaveDeleted")
      : translate("strSickLeaveDeleted");
  } else {
    return absence.typeIdentifier === EventType.ПлатенГодишенОтпуск
      ? translate("strPaidLeaveDeleted")
      : translate("strAbsenceDeleted");
  }
};

export const getLeaveUpdatedMessage = (absence: AbsenceWithType): string => {
  if (absence.isHospital) {
    return absence.typeIdentifier === EventType.БолниченПоБременност
      ? translate("strMaternityLeaveUpdatedSuccessfully")
      : translate("strSickLeaveUpdatedSuccessfully");
  } else {
    return absence.typeIdentifier === EventType.ПлатенГодишенОтпуск
      ? translate("strPaidLeaveUpdatedSuccessfully")
      : translate("strAbsenceUpdatedSuccessfully");
  }
};

export const getLeaveApprovedMessage = (absence: AbsenceWithType): string => {
  if (absence.isHospital) {
    return absence.typeIdentifier === EventType.БолниченПоБременност
      ? translate("strMaternityLeaveApproved")
      : translate("strSickLeaveApproved");
  } else {
    return absence.typeIdentifier === EventType.ПлатенГодишенОтпуск
      ? translate("strPaidLeaveApproved")
      : translate("strAbsenceApproved");
  }
};

export const getLeaveDeclinedMessage = (absence: AbsenceWithType): string => {
  if (absence.isHospital) {
    return absence.typeIdentifier === EventType.БолниченПоБременност
      ? translate("strMaternityLeaveDeclined")
      : translate("strSickLeaveDeclined");
  } else {
    return absence.typeIdentifier === EventType.ПлатенГодишенОтпуск
      ? translate("strPaidLeaveDeclined")
      : translate("strAbsenceDeclined");
  }
};
