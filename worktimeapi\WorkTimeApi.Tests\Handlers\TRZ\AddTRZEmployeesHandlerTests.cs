using Moq;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.TRZ;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Handlers.TRZ;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Services.Interfaces.TRZ;
using Microsoft.AspNetCore.Http;
using Gateway.Common.Results;

namespace WorkTimeApi.Tests.Handlers.TRZ;

public class AddTRZEmployeesHandlerTests
{
    private readonly Mock<ITRZMapper> _trzMapperMock;
    private readonly Mock<ITRZService> _trzServiceMock;
    private readonly AddTRZEmployeesHandler _handler;

    public AddTRZEmployeesHandlerTests()
    {
        _trzMapperMock = new Mock<ITRZMapper>();
        _trzServiceMock = new Mock<ITRZService>();
        _handler = new AddTRZEmployeesHandler(_trzMapperMock.Object, _trzServiceMock.Object);
    }

    [Fact]
    public async Task Handle_Should_Map_TRZEmployees_And_Call_Service_Successfully()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "John Doe",
                EGN = "1234567890",
                WorkTimeId = Guid.NewGuid()
            },
            new()
            {
                TRZEmployeeId = 2,
                Name = "Jane Smith",
                EGN = "0987654321",
                WorkTimeId = Guid.NewGuid()
            }
        };

        var mappedEmployees = new List<EmployeeDTO>
        {
            new()
            {
                WorkTimeId = trzEmployees[0].WorkTimeId!.Value,
                FirstName = "John",
                LastName = "Doe",
                CompanyId = companyId
            },
            new()
            {
                WorkTimeId = trzEmployees[1].WorkTimeId!.Value,
                FirstName = "Jane",
                LastName = "Smith",
                CompanyId = companyId
            }
        };

        var serviceResult = new Result<List<EmployeeDTO>>(mappedEmployees, default);

        var request = new AddTRZEmployeesRequest
        {
            CompanyId = companyId,
            TRZPendingEmployees = trzEmployees
        };

        _trzMapperMock.Setup(x => x.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId))
            .Returns(mappedEmployees);

        _trzServiceMock.Setup(x => x.AddTRZEmployeesAsync(mappedEmployees, companyId))
            .ReturnsAsync(serviceResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        _trzMapperMock.Verify(x => x.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId), Times.Once);
        _trzServiceMock.Verify(x => x.AddTRZEmployeesAsync(mappedEmployees, companyId), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Handle_Empty_TRZEmployees_List()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var emptyTrzEmployees = new List<TRZEmployeeDTO>();
        var emptyMappedEmployees = new List<EmployeeDTO>();
        var serviceResult = new Result<List<EmployeeDTO>>(emptyMappedEmployees, default);

        var request = new AddTRZEmployeesRequest
        {
            CompanyId = companyId,
            TRZPendingEmployees = emptyTrzEmployees
        };

        _trzMapperMock.Setup(x => x.MapTRZEmployeesDTOToEmployeesDTO(emptyTrzEmployees, companyId))
            .Returns(emptyMappedEmployees);

        _trzServiceMock.Setup(x => x.AddTRZEmployeesAsync(emptyMappedEmployees, companyId))
            .ReturnsAsync(serviceResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        _trzMapperMock.Verify(x => x.MapTRZEmployeesDTOToEmployeesDTO(emptyTrzEmployees, companyId), Times.Once);
        _trzServiceMock.Verify(x => x.AddTRZEmployeesAsync(emptyMappedEmployees, companyId), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Pass_Correct_CompanyId_To_Mapper_And_Service()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "Test Employee",
                EGN = "1234567890"
            }
        };

        var mappedEmployees = new List<EmployeeDTO>
        {
            new()
            {
                FirstName = "Test",
                LastName = "Employee",
                CompanyId = companyId
            }
        };

        var serviceResult = new Result<List<EmployeeDTO>>(mappedEmployees, default);

        var request = new AddTRZEmployeesRequest
        {
            CompanyId = companyId,
            TRZPendingEmployees = trzEmployees
        };

        _trzMapperMock.Setup(x => x.MapTRZEmployeesDTOToEmployeesDTO(It.IsAny<IEnumerable<TRZEmployeeDTO>>(), It.IsAny<Guid>()))
            .Returns(mappedEmployees);

        _trzServiceMock.Setup(x => x.AddTRZEmployeesAsync(It.IsAny<List<EmployeeDTO>>(), It.IsAny<Guid>()))
            .ReturnsAsync(serviceResult);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        _trzMapperMock.Verify(x => x.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId), Times.Once);
        _trzServiceMock.Verify(x => x.AddTRZEmployeesAsync(mappedEmployees, companyId), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Handle_CancellationToken()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 1,
                Name = "Test Employee",
                EGN = "1234567890"
            }
        };

        var mappedEmployees = new List<EmployeeDTO>
        {
            new()
            {
                FirstName = "Test",
                LastName = "Employee",
                CompanyId = companyId
            }
        };

        var serviceResult = new Result<List<EmployeeDTO>>(mappedEmployees, default);

        var request = new AddTRZEmployeesRequest
        {
            CompanyId = companyId,
            TRZPendingEmployees = trzEmployees
        };

        using var cts = new CancellationTokenSource();
        var cancellationToken = cts.Token;

        _trzMapperMock.Setup(x => x.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId))
            .Returns(mappedEmployees);

        _trzServiceMock.Setup(x => x.AddTRZEmployeesAsync(mappedEmployees, companyId))
            .ReturnsAsync(serviceResult);

        // Act & Assert
        var result = await _handler.Handle(request, cancellationToken);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task Handle_Should_Preserve_TRZEmployee_Properties_In_Mapping()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var worktimeId = Guid.NewGuid();
        var trzEmployees = new List<TRZEmployeeDTO>
        {
            new()
            {
                TRZEmployeeId = 123,
                Name = "John Michael Doe",
                EGN = "1234567890",
                WorkTimeId = worktimeId,
                EMail = "<EMAIL>",
                WorkPhone = "+1234567890",
                Gender = true,
                BirthDate = new DateTime(1990, 1, 1)
            }
        };

        var expectedMappedEmployees = new List<EmployeeDTO>
        {
            new()
            {
                WorkTimeId = worktimeId,
                TRZEmployeeId = 123,
                FirstName = "John",
                SecondName = "Michael",
                LastName = "Doe",
                CompanyId = companyId,
                WorkEmail = "<EMAIL>",
                WorkPhone = "+1234567890",
                BirthDate = new DateTime(1990, 1, 1)
            }
        };

        var serviceResult = new Result<List<EmployeeDTO>>(expectedMappedEmployees, default);

        var request = new AddTRZEmployeesRequest
        {
            CompanyId = companyId,
            TRZPendingEmployees = trzEmployees
        };

        _trzMapperMock.Setup(x => x.MapTRZEmployeesDTOToEmployeesDTO(trzEmployees, companyId))
            .Returns(expectedMappedEmployees);

        _trzServiceMock.Setup(x => x.AddTRZEmployeesAsync(expectedMappedEmployees, companyId))
            .ReturnsAsync(serviceResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        _trzMapperMock.Verify(x => x.MapTRZEmployeesDTOToEmployeesDTO(
            It.Is<IEnumerable<TRZEmployeeDTO>>(employees => 
                employees.First().TRZEmployeeId == 123 &&
                employees.First().Name == "John Michael Doe" &&
                employees.First().WorkTimeId == worktimeId), 
            companyId), Times.Once);
        _trzServiceMock.Verify(x => x.AddTRZEmployeesAsync(expectedMappedEmployees, companyId), Times.Once);
    }
}